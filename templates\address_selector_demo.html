<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重新设计的地址选择器</title>
    <style>
        :root {
            --primary-color: #4299e1;
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --border-radius-lg: 12px;
            --border-radius-xl: 16px;
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --transition-base: all 0.2s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-xl);
        }

        .demo-title {
            text-align: center;
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .demo-subtitle {
            text-align: center;
            color: #718096;
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 0.95rem;
            color: #2d3748;
            transition: var(--transition-base);
            background-color: #ffffff;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        /* 地址选择器样式 */
        .address-selector-section {
            background: rgba(248, 250, 252, 0.8);
            border-radius: var(--border-radius-lg);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid rgba(226, 232, 240, 0.8);
        }

        .region-select {
            background-color: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 0.95rem;
            color: #2d3748;
            transition: all 0.2s ease;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23718096' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 16px;
            padding-right: 2.5rem;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        .region-select:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .region-select:disabled {
            background-color: #f7fafc;
            color: #a0aec0;
            cursor: not-allowed;
            border-color: #e2e8f0;
        }

        .required-mark {
            color: #e53e3e;
            font-weight: 600;
        }

        #district-container {
            position: relative;
        }

        #district-input {
            background-color: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 0.95rem;
            color: #2d3748;
            transition: all 0.2s ease;
        }

        #district-input:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .btn {
            background: var(--primary-gradient);
            color: white;
            border: none;
            padding: 0.875rem 1.5rem;
            border-radius: var(--border-radius-lg);
            font-weight: 600;
            transition: var(--transition-base);
            box-shadow: var(--shadow-md);
            cursor: pointer;
            font-size: 0.95rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .result-section {
            background: rgba(248, 250, 252, 0.6);
            border-radius: var(--border-radius-lg);
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .result-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 1rem;
        }

        .result-content {
            background: #ffffff;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e2e8f0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: #4a5568;
            white-space: pre-wrap;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .demo-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">🎨 重新设计的地址选择器</h1>
        <p class="demo-subtitle">更美观、更实用的省市县三级联动选择器</p>
        
        <form id="addressForm">
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label" for="recipientName">收货人姓名 <span class="required-mark">*</span></label>
                    <input type="text" class="form-control" id="recipientName" name="recipient_name" placeholder="请输入收货人姓名" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="phone">联系电话 <span class="required-mark">*</span></label>
                    <input type="tel" class="form-control" id="phone" name="phone" placeholder="请输入联系电话" required>
                </div>
            </div>
            
            <!-- 地址选择区域 -->
            <div class="address-selector-section">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="province">省份 <span class="required-mark">*</span></label>
                        <select class="form-control region-select" id="province" name="province" required>
                            <option value="">请选择省份</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="city">城市 <span class="required-mark">*</span></label>
                        <select class="form-control region-select" id="city" name="city" required>
                            <option value="">请选择城市</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="district">区县</label>
                        <div id="district-container">
                            <select class="form-control region-select" id="district" name="district" style="display: none;">
                                <option value="">请选择区县</option>
                            </select>
                            <input type="text" class="form-control" id="district-input" name="district_input" 
                                   placeholder="请输入区县名称" style="display: none;">
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="postalCode">邮政编码</label>
                        <input type="text" class="form-control" id="postalCode" name="postal_code" 
                               placeholder="请输入邮政编码">
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="addressDetail">详细地址 <span class="required-mark">*</span></label>
                <input type="text" class="form-control" id="addressDetail" name="address_detail" 
                       placeholder="街道门牌号等详细信息" required>
            </div>
            
            <div style="text-align: center;">
                <button type="button" class="btn" onclick="getSelectedRegion()">获取选中地址</button>
            </div>
        </form>
        
        <div class="result-section">
            <h3 class="result-title">📍 当前选中地址</h3>
            <div id="selectedRegion" class="result-content">等待选择...</div>
        </div>
    </div>

    <script src="/static/js/simple-region-selector.js"></script>
    <script>
        let regionSelector = null;
        
        function getSelectedRegion() {
            if (!regionSelector) {
                document.getElementById('selectedRegion').textContent = '地址选择器未初始化';
                return;
            }
            
            const region = regionSelector.getSelectedRegion();
            const recipientName = document.getElementById('recipientName').value;
            const phone = document.getElementById('phone').value;
            const addressDetail = document.getElementById('addressDetail').value;
            const postalCode = document.getElementById('postalCode').value;
            
            const fullAddress = {
                recipient_name: recipientName,
                phone: phone,
                province: region.province.name,
                city: region.city.name,
                district: region.district.name,
                address_detail: addressDetail,
                postal_code: postalCode,
                full_address: `${region.province.name} ${region.city.name} ${region.district.name} ${addressDetail}`
            };
            
            document.getElementById('selectedRegion').textContent = JSON.stringify(fullAddress, null, 2);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('演示页面加载完成，初始化地址选择器...');
            
            try {
                regionSelector = new SimpleRegionSelector({
                    provinceSelector: '#province',
                    citySelector: '#city',
                    districtSelector: '#district',
                    provincePlaceholder: '请选择省份',
                    cityPlaceholder: '请选择城市',
                    districtPlaceholder: '请选择区县',
                    onChange: function(region) {
                        console.log('地址选择变化:', region);
                        // 自动更新显示
                        getSelectedRegion();
                    }
                });
                
                console.log('地址选择器初始化成功');
                
            } catch (error) {
                console.error('地址选择器初始化失败:', error);
                document.getElementById('selectedRegion').textContent = '初始化失败: ' + error.message;
            }
        });
    </script>
</body>
</html>

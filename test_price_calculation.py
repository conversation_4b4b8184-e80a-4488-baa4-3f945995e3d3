#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json

def test_price_calculation():
    """测试价格计算功能"""
    
    print("🧪 测试价格计算功能")
    
    # 测试有价格调整的属性
    test_data = {
        "product_id": 1,
        "quantity": 1,
        "selected_attributes": [55, 58, 61],  # 欧式名片(+5) + 350g艺术纸(+10) + 200张(+15)
        "custom_attributes": {
            "64": "测试公司",
            "65": "13800138000"
        }
    }
    
    response = requests.post(
        "http://127.0.0.1:5000/api/attributes/calculate-price",
        headers={"Content-Type": "application/json"},
        json=test_data
    )
    
    if response.status_code == 200:
        data = response.json()
        if data["success"]:
            price_data = data["data"]
            print(f"基础价格: ¥{price_data['base_price']:.2f}")
            print(f"属性加价: ¥{price_data['total_attribute_price']:.2f}")
            print(f"单价: ¥{price_data['unit_price']:.2f}")
            print(f"总价: ¥{price_data['total_price']:.2f}")
            print("属性详情:")
            for detail in price_data["attribute_details"]:
                print(f"  - {detail['name']}: {detail['value']} (+¥{detail['calculated_price']:.2f})")
        else:
            print(f"计算失败: {data['message']}")
    else:
        print(f"请求失败: {response.status_code}")
        print(f"响应内容: {response.text}")

if __name__ == "__main__":
    test_price_calculation()

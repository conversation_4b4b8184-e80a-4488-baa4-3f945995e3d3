#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
为attribute_groups表添加自定义输入支持字段的迁移脚本
"""

from app import create_app
from models import db
from sqlalchemy import text

def run_migration():
    """执行数据库迁移"""

    app = create_app()

    with app.app_context():
        try:
            print("attribute_groups表存在，开始执行迁移...")

            # 定义要添加的列
            alter_statements = [
                ("allow_custom_input", "ALTER TABLE attribute_groups ADD COLUMN allow_custom_input BOOLEAN DEFAULT FALSE"),
                ("custom_input_type", "ALTER TABLE attribute_groups ADD COLUMN custom_input_type ENUM('text', 'number', 'textarea') DEFAULT 'text'"),
                ("custom_input_label", "ALTER TABLE attribute_groups ADD COLUMN custom_input_label VARCHAR(100) DEFAULT '自定义'"),
                ("custom_input_placeholder", "ALTER TABLE attribute_groups ADD COLUMN custom_input_placeholder VARCHAR(200)"),
                ("custom_validation_pattern", "ALTER TABLE attribute_groups ADD COLUMN custom_validation_pattern VARCHAR(500)"),
                ("custom_validation_message", "ALTER TABLE attribute_groups ADD COLUMN custom_validation_message VARCHAR(200)"),
                ("custom_price_formula", "ALTER TABLE attribute_groups ADD COLUMN custom_price_formula TEXT"),
                ("custom_price_modifier", "ALTER TABLE attribute_groups ADD COLUMN custom_price_modifier DECIMAL(15,5) DEFAULT 0.00000"),
                ("custom_price_modifier_type", "ALTER TABLE attribute_groups ADD COLUMN custom_price_modifier_type ENUM('fixed', 'percentage', 'formula') DEFAULT 'fixed'")
            ]

            # 检查现有列
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            existing_columns = [col['name'] for col in inspector.get_columns('attribute_groups')]
            print(f"现有列: {existing_columns}")

            # 执行ALTER TABLE语句
            for column_name, statement in alter_statements:
                if column_name not in existing_columns:
                    try:
                        print(f"添加列: {column_name}")
                        db.session.execute(text(statement))
                        print(f"  ✅ 成功添加列: {column_name}")
                    except Exception as e:
                        print(f"  ❌ 添加列失败: {column_name}, 错误: {e}")
                        # 如果是MySQL，可能需要不同的语法
                        if 'ENUM' in statement and 'syntax error' in str(e).lower():
                            # 尝试MySQL兼容的语法
                            mysql_statement = statement.replace("ENUM('text', 'number', 'textarea')", "VARCHAR(20)")
                            mysql_statement = mysql_statement.replace("ENUM('fixed', 'percentage', 'formula')", "VARCHAR(20)")
                            try:
                                db.session.execute(text(mysql_statement))
                                print(f"  ✅ 使用MySQL语法成功添加列: {column_name}")
                            except Exception as e2:
                                print(f"  ❌ MySQL语法也失败: {e2}")
                else:
                    print(f"  ⏭️ 列已存在，跳过: {column_name}")

            # 提交更改
            db.session.commit()
            print("\n数据库迁移执行成功！")

            # 验证结果
            updated_columns = [col['name'] for col in inspector.get_columns('attribute_groups')]
            print(f"\n更新后的属性组表结构 ({len(updated_columns)} 列):")
            for col_name in updated_columns:
                print(f"  - {col_name}")

            # 更新示例数据（如果表中有数据）
            result = db.session.execute(text("SELECT COUNT(*) FROM attribute_groups"))
            count = result.scalar()

            if count > 0:
                print(f"\n发现 {count} 个属性组，更新示例数据...")

                # 为纸张属性组启用自定义输入
                db.session.execute(text("""
                    UPDATE attribute_groups
                    SET
                        allow_custom_input = 1,
                        custom_input_type = 'text',
                        custom_input_label = '自定义',
                        custom_input_placeholder = '请输入自定义纸张类型',
                        custom_price_modifier = 5.00000,
                        custom_price_modifier_type = 'fixed'
                    WHERE name = '纸张'
                """))

                # 为每本张数属性组启用自定义输入
                db.session.execute(text("""
                    UPDATE attribute_groups
                    SET
                        allow_custom_input = 1,
                        custom_input_type = 'number',
                        custom_input_label = '自定义',
                        custom_input_placeholder = '请输入自定义张数',
                        custom_validation_pattern = '^[1-9][0-9]*$',
                        custom_validation_message = '请输入大于0的整数',
                        custom_price_formula = 'numeric_value * 0.1',
                        custom_price_modifier_type = 'formula'
                    WHERE name = '每本张数'
                """))

                db.session.commit()
                print("示例数据更新完成")

            return True

        except Exception as e:
            print(f"迁移执行失败: {e}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    print("开始执行attribute_groups表自定义输入字段迁移...")
    success = run_migration()
    if success:
        print("✅ 迁移完成！")
    else:
        print("❌ 迁移失败！")

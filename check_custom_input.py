#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查自定义输入配置
"""

from app import create_app
from models import db
from models.product import AttributeGroup, Attribute, Category, Product

def check_custom_input_config():
    """检查自定义输入配置"""
    
    app = create_app()
    
    with app.app_context():
        print("🔍 检查自定义输入配置...")
        
        # 1. 检查启用自定义输入的属性组
        custom_groups = AttributeGroup.query.filter_by(allow_custom_input=True).all()
        print(f"\n📋 启用自定义输入的属性组 ({len(custom_groups)} 个):")
        
        for group in custom_groups:
            print(f"  - {group.name}")
            print(f"    输入类型: {group.custom_input_type}")
            print(f"    标签: {group.custom_input_label}")
            print(f"    占位符: {group.custom_input_placeholder}")
            print(f"    价格计算: {group.custom_price_modifier_type}")
            if group.custom_price_modifier_type == 'formula':
                print(f"    公式: {group.custom_price_formula}")
            else:
                print(f"    调整值: {group.custom_price_modifier}")
        
        # 2. 检查产品1的属性组配置
        product = Product.query.get(1)
        if product and product.category_id:
            print(f"\n🛍️ 产品1 ({product.name}) 的属性组配置:")
            category = Category.query.get(product.category_id)
            if category:
                print(f"  分类: {category.name}")
                bound_groups = category.attribute_groups.filter_by(is_active=True).all()
                
                for group in bound_groups:
                    print(f"  - 属性组: {group.name}")
                    print(f"    允许自定义: {group.allow_custom_input}")
                    if group.allow_custom_input:
                        print(f"    自定义配置: {group.custom_input_type}, '{group.custom_input_label}'")
                    
                    # 显示该组的属性
                    attributes = Attribute.query.filter_by(group_id=group.id, is_active=True).all()
                    print(f"    属性数量: {len(attributes)}")
                    for attr in attributes[:3]:  # 只显示前3个
                        print(f"      - {attr.name}: {attr.value}")
        
        # 3. 模拟前端数据结构
        print(f"\n🌐 模拟前端数据结构:")
        if product and product.category_id:
            category = Category.query.get(product.category_id)
            if category:
                bound_groups = category.attribute_groups.filter_by(is_active=True).order_by(AttributeGroup.sort_order, AttributeGroup.name).all()
                attribute_groups_with_config = []
                
                for group in bound_groups:
                    group_attributes = Attribute.query.filter_by(group_id=group.id, is_active=True).order_by(Attribute.sort_order, Attribute.name).all()
                    
                    if group_attributes or group.allow_custom_input:
                        config = {
                            'group': group,
                            'attributes': group_attributes,
                            'allow_custom_input': group.allow_custom_input,
                            'custom_input_type': group.custom_input_type,
                            'custom_input_label': group.custom_input_label,
                            'custom_input_placeholder': group.custom_input_placeholder
                        }
                        attribute_groups_with_config.append(config)
                        
                        print(f"  - {group.name}:")
                        print(f"    属性数量: {len(group_attributes)}")
                        print(f"    允许自定义: {group.allow_custom_input}")
                        if group.allow_custom_input:
                            print(f"    自定义标签: '{group.custom_input_label}'")
                            print(f"    输入类型: {group.custom_input_type}")
                
                print(f"\n📊 前端将显示 {len(attribute_groups_with_config)} 个属性组")
                
                # 检查是否有自定义输入选项
                custom_enabled_count = sum(1 for config in attribute_groups_with_config if config['allow_custom_input'])
                print(f"其中 {custom_enabled_count} 个启用了自定义输入")
                
                if custom_enabled_count > 0:
                    print("✅ 前端应该显示自定义输入选项")
                else:
                    print("❌ 前端不会显示自定义输入选项")
        
        return True

if __name__ == "__main__":
    check_custom_input_config()
    print("\n📝 检查完成")

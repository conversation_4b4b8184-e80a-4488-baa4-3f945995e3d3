#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
扩展属性系统数据库迁移脚本
执行数据库结构更新以支持新的属性类型
"""

import os
import sys
sys.path.insert(0, os.path.abspath('.'))

from app import create_app
from models import db
from sqlalchemy import text

def run_migration():
    """执行数据库迁移"""
    app = create_app()
    
    with app.app_context():
        try:
            print("开始执行扩展属性系统数据库迁移...")
            
            # 读取迁移SQL文件
            migration_file = 'migrations/add_extended_attributes.sql'
            if not os.path.exists(migration_file):
                print(f"错误：找不到迁移文件 {migration_file}")
                return False
            
            with open(migration_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割SQL语句（按分号分割，但忽略注释）
            sql_statements = []
            current_statement = ""
            
            for line in sql_content.split('\n'):
                line = line.strip()
                
                # 跳过注释行
                if line.startswith('--') or not line:
                    continue
                
                current_statement += line + " "
                
                # 如果行以分号结尾，表示一个完整的SQL语句
                if line.endswith(';'):
                    sql_statements.append(current_statement.strip())
                    current_statement = ""
            
            # 执行每个SQL语句
            success_count = 0
            for i, statement in enumerate(sql_statements, 1):
                if not statement or statement == ';':
                    continue
                
                try:
                    print(f"执行语句 {i}/{len(sql_statements)}: {statement[:50]}...")
                    db.session.execute(text(statement))
                    db.session.commit()
                    success_count += 1
                    print(f"  ✅ 成功")
                    
                except Exception as e:
                    error_msg = str(e)
                    
                    # 检查是否是可以忽略的错误
                    ignorable_errors = [
                        "Duplicate column name",
                        "Multiple primary key defined",
                        "Table already exists",
                        "Duplicate key name"
                    ]
                    
                    is_ignorable = any(err in error_msg for err in ignorable_errors)
                    
                    if is_ignorable:
                        print(f"  ⚠️ 警告（已忽略）: {error_msg}")
                        success_count += 1
                    else:
                        print(f"  ❌ 错误: {error_msg}")
                        print(f"  SQL: {statement}")
                        # 继续执行其他语句，不要因为一个错误就停止
                        continue
            
            print(f"\n迁移完成！成功执行 {success_count}/{len(sql_statements)} 个语句")
            
            # 验证迁移结果
            print("\n验证迁移结果...")
            verify_migration()
            
            return True
            
        except Exception as e:
            print(f"迁移失败: {str(e)}")
            db.session.rollback()
            return False

def verify_migration():
    """验证迁移结果"""
    try:
        # 检查新字段是否存在
        result = db.session.execute(text("""
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'attributes' 
            AND COLUMN_NAME IN (
                'attribute_category', 
                'validation_rules', 
                'price_calculation_rules',
                'file_accept_types',
                'number_min',
                'is_multiple'
            )
        """))
        
        new_columns = [row[0] for row in result.fetchall()]
        expected_columns = [
            'attribute_category', 
            'validation_rules', 
            'price_calculation_rules',
            'file_accept_types',
            'number_min',
            'is_multiple'
        ]
        
        print(f"新增字段检查: {len(new_columns)}/{len(expected_columns)} 个字段已添加")
        for col in expected_columns:
            if col in new_columns:
                print(f"  ✅ {col}")
            else:
                print(f"  ❌ {col} (缺失)")
        
        # 检查新表是否存在
        result = db.session.execute(text("""
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME IN ('attribute_values', 'attribute_combination_prices', 'attribute_templates')
        """))
        
        new_tables = [row[0] for row in result.fetchall()]
        expected_tables = ['attribute_values', 'attribute_combination_prices', 'attribute_templates']
        
        print(f"\n新增表检查: {len(new_tables)}/{len(expected_tables)} 个表已创建")
        for table in expected_tables:
            if table in new_tables:
                print(f"  ✅ {table}")
            else:
                print(f"  ❌ {table} (缺失)")
        
        # 检查枚举值是否更新
        result = db.session.execute(text("""
            SELECT COLUMN_TYPE 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'attributes' 
            AND COLUMN_NAME = 'input_type'
        """))
        
        column_type = result.fetchone()
        if column_type:
            enum_values = column_type[0]
            new_types = ['text_input', 'number_input', 'file_upload', 'color_picker']
            
            print(f"\n枚举类型检查:")
            for enum_type in new_types:
                if enum_type in enum_values:
                    print(f"  ✅ {enum_type}")
                else:
                    print(f"  ❌ {enum_type} (缺失)")
        
        print("\n✅ 迁移验证完成！")
        
    except Exception as e:
        print(f"验证过程中出现错误: {str(e)}")

def rollback_migration():
    """回滚迁移（可选功能）"""
    app = create_app()
    
    with app.app_context():
        try:
            print("开始回滚扩展属性系统迁移...")
            
            # 删除新增的表
            rollback_statements = [
                "DROP TABLE IF EXISTS attribute_templates;",
                "DROP TABLE IF EXISTS attribute_combination_prices;", 
                "DROP TABLE IF EXISTS attribute_values;",
                
                # 删除新增的字段
                "ALTER TABLE attributes DROP COLUMN IF EXISTS max_selections;",
                "ALTER TABLE attributes DROP COLUMN IF EXISTS is_multiple;",
                "ALTER TABLE attributes DROP COLUMN IF EXISTS number_step;",
                "ALTER TABLE attributes DROP COLUMN IF EXISTS number_max;",
                "ALTER TABLE attributes DROP COLUMN IF EXISTS number_min;",
                "ALTER TABLE attributes DROP COLUMN IF EXISTS file_max_size;",
                "ALTER TABLE attributes DROP COLUMN IF EXISTS file_accept_types;",
                "ALTER TABLE attributes DROP COLUMN IF EXISTS price_calculation_rules;",
                "ALTER TABLE attributes DROP COLUMN IF EXISTS validation_rules;",
                "ALTER TABLE attributes DROP COLUMN IF EXISTS attribute_category;",
                
                # 恢复原始的枚举类型
                """ALTER TABLE attributes MODIFY COLUMN input_type ENUM(
                    'select', 'radio', 'input', 'textarea', 'checkbox'
                ) DEFAULT 'select';"""
            ]
            
            for statement in rollback_statements:
                try:
                    print(f"执行回滚: {statement[:50]}...")
                    db.session.execute(text(statement))
                    db.session.commit()
                    print("  ✅ 成功")
                except Exception as e:
                    print(f"  ⚠️ 警告: {str(e)}")
                    continue
            
            print("✅ 回滚完成！")
            
        except Exception as e:
            print(f"回滚失败: {str(e)}")
            db.session.rollback()

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='扩展属性系统数据库迁移')
    parser.add_argument('--rollback', action='store_true', help='回滚迁移')
    
    args = parser.parse_args()
    
    if args.rollback:
        rollback_migration()
    else:
        success = run_migration()
        if not success:
            print("迁移失败，请检查错误信息")
            sys.exit(1)
        else:
            print("迁移成功完成！")

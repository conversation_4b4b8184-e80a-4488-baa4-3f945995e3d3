import sqlite3

conn = sqlite3.connect('print_service.db')
cursor = conn.cursor()

# 检查所有表
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = [table[0] for table in cursor.fetchall()]
print('数据库中的表:', tables)

# 如果有attribute_groups表，查看其结构
if 'attribute_groups' in tables:
    cursor.execute("PRAGMA table_info(attribute_groups)")
    columns = cursor.fetchall()
    print('\nattribute_groups表结构:')
    for col in columns:
        print(f'  {col[1]} - {col[2]}')

conn.close()

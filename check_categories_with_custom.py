#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查哪些分类有自定义输入属性组
"""

from app import create_app
from models import db
from models.product import AttributeGroup, Attribute, Category, Product

def check_categories_with_custom():
    """检查哪些分类有自定义输入属性组"""
    
    app = create_app()
    
    with app.app_context():
        print("🔍 检查分类和自定义输入属性组的关系...")
        
        # 1. 查看所有分类
        categories = Category.query.filter_by(is_active=True).all()
        print(f"\n📂 所有分类 ({len(categories)} 个):")
        
        for category in categories:
            print(f"  - {category.name} (ID: {category.id})")
            
            # 查看该分类的属性组
            groups = category.attribute_groups.filter_by(is_active=True).all()
            print(f"    属性组数量: {len(groups)}")
            
            custom_groups = [g for g in groups if g.allow_custom_input]
            if custom_groups:
                print(f"    ✅ 启用自定义输入的属性组: {len(custom_groups)} 个")
                for group in custom_groups:
                    print(f"      - {group.name} ({group.custom_input_type})")
            else:
                print(f"    ❌ 无启用自定义输入的属性组")
        
        # 2. 查看所有启用自定义输入的属性组
        custom_groups = AttributeGroup.query.filter_by(allow_custom_input=True, is_active=True).all()
        print(f"\n🎛️ 所有启用自定义输入的属性组 ({len(custom_groups)} 个):")
        
        for group in custom_groups:
            print(f"  - {group.name} (ID: {group.id})")
            print(f"    分类ID: {group.category_id}")
            if group.category_id:
                category = Category.query.get(group.category_id)
                if category:
                    print(f"    分类名称: {category.name}")
                else:
                    print(f"    分类名称: 未找到")
            else:
                print(f"    分类名称: 未绑定")
        
        # 3. 查看所有产品及其分类
        products = Product.query.filter_by(is_active=True).all()
        print(f"\n🛍️ 所有产品 ({len(products)} 个):")
        
        for product in products:
            print(f"  - {product.name} (ID: {product.id})")
            if product.category_id:
                category = Category.query.get(product.category_id)
                if category:
                    print(f"    分类: {category.name}")
                    
                    # 检查该分类是否有自定义输入属性组
                    custom_groups = category.attribute_groups.filter_by(is_active=True, allow_custom_input=True).all()
                    if custom_groups:
                        print(f"    ✅ 可使用自定义输入 ({len(custom_groups)} 个属性组)")
                        print(f"    🌐 访问链接: http://127.0.0.1:5000/product/{product.id}")
                    else:
                        print(f"    ❌ 无自定义输入功能")
                else:
                    print(f"    分类: 未找到")
            else:
                print(f"    分类: 未绑定")
        
        # 4. 建议操作
        print(f"\n💡 建议操作:")
        
        # 找到有自定义输入属性组的分类
        categories_with_custom = []
        for category in categories:
            custom_groups = category.attribute_groups.filter_by(is_active=True, allow_custom_input=True).all()
            if custom_groups:
                categories_with_custom.append((category, custom_groups))
        
        if categories_with_custom:
            print(f"1. 以下分类已启用自定义输入功能:")
            for category, groups in categories_with_custom:
                print(f"   - {category.name}: {len(groups)} 个自定义属性组")
                
                # 查找该分类的产品
                products_in_category = Product.query.filter_by(category_id=category.id, is_active=True).all()
                if products_in_category:
                    print(f"     可测试产品:")
                    for product in products_in_category[:3]:  # 只显示前3个
                        print(f"       - {product.name}: http://127.0.0.1:5000/product/{product.id}")
                else:
                    print(f"     ⚠️ 该分类下暂无产品")
        else:
            print(f"1. 暂无分类启用自定义输入功能")
            print(f"2. 建议为现有分类的属性组启用自定义输入:")
            for category in categories[:3]:  # 只显示前3个分类
                print(f"   - 编辑分类 '{category.name}' 下的属性组")
                print(f"   - 访问: http://127.0.0.1:5000/admin/attribute-groups?category_id={category.id}")

if __name__ == "__main__":
    check_categories_with_custom()
    print("\n📝 检查完成")

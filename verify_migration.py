#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证数据库迁移结果
"""

from app import create_app
from models import db
from sqlalchemy import text, inspect

def verify_migration():
    """验证迁移结果"""
    
    app = create_app()
    
    with app.app_context():
        try:
            # 检查表结构
            inspector = inspect(db.engine)
            columns = inspector.get_columns('attribute_groups')
            
            print(f"attribute_groups表结构 ({len(columns)} 列):")
            for col in columns:
                print(f"  - {col['name']}: {col['type']}")
            
            # 检查新增的列
            new_columns = [
                'allow_custom_input',
                'custom_input_type', 
                'custom_input_label',
                'custom_input_placeholder',
                'custom_validation_pattern',
                'custom_validation_message',
                'custom_price_formula',
                'custom_price_modifier',
                'custom_price_modifier_type'
            ]
            
            existing_column_names = [col['name'] for col in columns]
            
            print(f"\n新增列检查:")
            for col_name in new_columns:
                if col_name in existing_column_names:
                    print(f"  ✅ {col_name} - 存在")
                else:
                    print(f"  ❌ {col_name} - 不存在")
            
            # 检查示例数据
            result = db.session.execute(text("""
                SELECT name, allow_custom_input, custom_input_type, custom_input_label 
                FROM attribute_groups 
                WHERE allow_custom_input = 1
            """))
            
            custom_groups = result.fetchall()
            print(f"\n启用自定义输入的属性组 ({len(custom_groups)} 个):")
            for group in custom_groups:
                print(f"  - {group[0]}: {group[2]} ({group[3]})")
            
            return True
            
        except Exception as e:
            print(f"验证失败: {e}")
            return False

if __name__ == "__main__":
    print("开始验证数据库迁移结果...")
    success = verify_migration()
    if success:
        print("\n✅ 验证完成！")
    else:
        print("\n❌ 验证失败！")

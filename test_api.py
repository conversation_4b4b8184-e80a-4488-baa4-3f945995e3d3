#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json

# 测试属性API
def test_attribute_api():
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 测试商品属性API")
    
    # 1. 测试获取商品属性
    print("\n1. 获取商品属性...")
    response = requests.get(f"{base_url}/api/attributes/product/1")
    
    if response.status_code == 200:
        data = response.json()
        if data['success']:
            print("✅ 获取属性成功")
            print(f"   属性组数量: {len(data['data'])}")
            for group in data['data']:
                print(f"   - {group['name']}: {len(group['attributes'])} 个属性")
        else:
            print(f"❌ 获取属性失败: {data['message']}")
    else:
        print(f"❌ 请求失败: {response.status_code}")
        return False
    
    # 2. 测试价格计算
    print("\n2. 测试价格计算...")
    
    # 选择一些属性进行测试
    test_data = {
        "product_id": 1,
        "quantity": 1,
        "selected_attributes": [54, 57, 60],  # 标准名片 + 300g铜版纸 + 100张
        "custom_attributes": {
            "64": "测试公司",
            "65": "13800138000"
        },
        "attribute_quantities": {}
    }
    
    response = requests.post(
        f"{base_url}/api/attributes/calculate-price",
        headers={'Content-Type': 'application/json'},
        json=test_data
    )
    
    if response.status_code == 200:
        data = response.json()
        if data['success']:
            print("✅ 价格计算成功")
            price_data = data['data']
            print(f"   基础价格: ¥{price_data['base_price']:.2f}")
            print(f"   属性加价: ¥{price_data['total_attribute_price']:.2f}")
            print(f"   单价: ¥{price_data['unit_price']:.2f}")
            print(f"   总价: ¥{price_data['total_price']:.2f}")
            
            if price_data['attribute_details']:
                print("   属性详情:")
                for detail in price_data['attribute_details']:
                    print(f"     - {detail['name']}: {detail['value']} (+¥{detail['calculated_price']:.2f})")
        else:
            print(f"❌ 价格计算失败: {data['message']}")
    else:
        print(f"❌ 请求失败: {response.status_code}")
        print(f"   响应内容: {response.text}")
        return False
    
    # 3. 测试不同数量的价格计算
    print("\n3. 测试不同数量的价格计算...")
    
    for quantity in [1, 2, 5]:
        test_data['quantity'] = quantity
        response = requests.post(
            f"{base_url}/api/attributes/calculate-price",
            headers={'Content-Type': 'application/json'},
            json=test_data
        )
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                price_data = data['data']
                print(f"   数量 {quantity}: 总价 ¥{price_data['total_price']:.2f}")
            else:
                print(f"   数量 {quantity}: 计算失败 - {data['message']}")
        else:
            print(f"   数量 {quantity}: 请求失败 - {response.status_code}")
    
    # 4. 测试属性验证
    print("\n4. 测试属性验证...")
    
    validation_data = {
        "attribute_values": {
            "64": "测试公司名称",
            "65": "13800138000",
            "66": "这是一个特殊要求的测试"
        }
    }
    
    response = requests.post(
        f"{base_url}/api/attributes/validate",
        headers={'Content-Type': 'application/json'},
        json=validation_data
    )
    
    if response.status_code == 200:
        data = response.json()
        if data['success']:
            print("✅ 属性验证成功")
            for attr_id, result in data['data'].items():
                status = "✅" if result['valid'] else "❌"
                message = f" - {result['message']}" if result['message'] else ""
                print(f"   属性 {attr_id}: {status}{message}")
        else:
            print(f"❌ 属性验证失败: {data['message']}")
    else:
        print(f"❌ 验证请求失败: {response.status_code}")
    
    print("\n🎉 API测试完成！")
    return True

if __name__ == '__main__':
    try:
        test_attribute_api()
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试前端自定义输入显示
"""

import requests
from bs4 import BeautifulSoup

def test_frontend_custom_input():
    """测试前端自定义输入功能显示"""
    
    print("🌐 测试前端自定义输入功能显示...")
    
    try:
        # 访问产品详情页面
        url = "http://127.0.0.1:5000/product/1"
        response = requests.get(url)
        
        if response.status_code != 200:
            print(f"❌ 无法访问产品页面，状态码: {response.status_code}")
            return False
        
        # 解析HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 检查是否有规格选择区域
        spec_section = soup.find('div', class_='spec-section')
        if not spec_section:
            print("❌ 未找到规格选择区域")
            return False
        
        print("✅ 找到规格选择区域")
        
        # 检查下拉框
        select_elements = spec_section.find_all('select', class_='spec-select')
        print(f"📋 找到 {len(select_elements)} 个属性选择下拉框")
        
        custom_options_found = 0
        custom_input_wrappers_found = 0
        
        for select in select_elements:
            group_name = select.get('data-group', '未知')
            allow_custom = select.get('data-allow-custom', 'false')
            
            print(f"  - 属性组: {group_name}")
            print(f"    允许自定义: {allow_custom}")
            
            # 检查是否有自定义选项
            custom_option = select.find('option', {'value': 'custom'})
            if custom_option:
                custom_options_found += 1
                custom_label = custom_option.get_text().strip()
                print(f"    ✅ 找到自定义选项: '{custom_label}'")
                
                # 检查对应的自定义输入框
                group_id = group_name.replace(' ', '_')
                custom_wrapper = soup.find('div', id=f'customInput_{group_id}')
                if custom_wrapper:
                    custom_input_wrappers_found += 1
                    input_field = custom_wrapper.find(['input', 'textarea'])
                    if input_field:
                        input_type = input_field.get('type', input_field.name)
                        placeholder = input_field.get('placeholder', '')
                        print(f"    ✅ 找到自定义输入框: {input_type}, 占位符: '{placeholder}'")
                    else:
                        print(f"    ❌ 自定义输入框容器存在但未找到输入字段")
                else:
                    print(f"    ❌ 未找到对应的自定义输入框容器")
            else:
                print(f"    ⚪ 无自定义选项")
        
        # 检查JavaScript函数
        script_tags = soup.find_all('script')
        js_functions_found = []
        
        for script in script_tags:
            if script.string:
                content = script.string
                if 'handleAttributeSelectChange' in content:
                    js_functions_found.append('handleAttributeSelectChange')
                if 'handleCustomGroupInput' in content:
                    js_functions_found.append('handleCustomGroupInput')
                if 'validateCustomGroupInput' in content:
                    js_functions_found.append('validateCustomGroupInput')
        
        print(f"\n📜 JavaScript函数检查:")
        for func in ['handleAttributeSelectChange', 'handleCustomGroupInput', 'validateCustomGroupInput']:
            if func in js_functions_found:
                print(f"  ✅ {func}")
            else:
                print(f"  ❌ {func}")
        
        # 生成测试报告
        print(f"\n📊 测试结果:")
        print(f"  - 属性选择下拉框: {len(select_elements)} 个")
        print(f"  - 自定义选项: {custom_options_found} 个")
        print(f"  - 自定义输入框: {custom_input_wrappers_found} 个")
        print(f"  - JavaScript函数: {len(js_functions_found)}/3 个")
        
        if custom_options_found > 0 and custom_input_wrappers_found > 0:
            print("\n🎉 前端自定义输入功能显示正常！")
            return True
        else:
            print("\n❌ 前端自定义输入功能显示异常")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_admin_backend():
    """测试管理后台自定义输入配置"""
    print("\n🔧 管理后台测试建议:")
    print("1. 访问 http://127.0.0.1:5000/admin/attribute-groups")
    print("2. 编辑一个属性组")
    print("3. 启用'允许自定义输入'")
    print("4. 配置自定义输入类型、标签、占位符")
    print("5. 设置价格计算规则")
    print("6. 保存后访问对应的产品页面")
    print("7. 验证自定义选项是否正确显示")

if __name__ == "__main__":
    print("🚀 开始前端自定义输入功能测试...")
    
    success = test_frontend_custom_input()
    
    if success:
        print("\n✅ 前端测试通过！")
    else:
        print("\n❌ 前端测试失败")
    
    test_admin_backend()
    print("\n📝 测试完成")

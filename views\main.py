from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, abort, current_app
from flask_login import login_required, current_user
from sqlalchemy import or_
from models import db
from models.product import Product, Category, Attribute, AttributeGroup
from models.order import QuantityDiscount, Order, OrderItem
from models.system import SystemConfig, Region
from services.payment import PaymentService
from decimal import Decimal
import uuid
import json
from collections import OrderedDict

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """首页 - 重定向到现代化版本"""
    return render_template('main/index_modern.html')

# ========== 商品相关路由 ==========

@main_bp.route('/products')
def products():
    """商品列表 - 重定向到现代化版本"""
    page = request.args.get('page', 1, type=int)
    category_id = request.args.get('category', type=int)
    search = request.args.get('search', '')
    sort = request.args.get('sort', 'default')
    
    query = Product.query.filter_by(is_active=True)
    
    # 分类筛选
    if category_id:
        category = Category.query.get_or_404(category_id)
        # 包含子分类
        category_ids = [category.id]
        category_ids.extend([child.id for child in category.get_all_children()])
        query = query.filter(Product.category_id.in_(category_ids))
    
    # 搜索
    if search:
        query = query.filter(or_(
            Product.name.contains(search),
            Product.description.contains(search)
        ))
    
    # 排序
    if sort == 'price_asc':
        query = query.order_by(Product.base_price.asc())
    elif sort == 'price_desc':
        query = query.order_by(Product.base_price.desc())
    elif sort == 'name':
        query = query.order_by(Product.name.asc())
    elif sort == 'newest':
        query = query.order_by(Product.created_at.desc())
    else:
        query = query.order_by(Product.sort_order.asc(), Product.id.desc())
    
    products = query.paginate(
        page=page, 
        per_page=20, 
        error_out=False
    )
    
    # 获取所有分类用于筛选
    categories = Category.query.filter_by(is_active=True).order_by(Category.sort_order).all()
    
    return render_template('main/products_modern.html', 
                           products=products, 
                           categories=categories,
                           current_category=category_id,
                           search=search,
                           sort=sort)

@main_bp.route('/product/<int:id>')
def product_detail(id):
    """商品详情 - 现代化版本"""
    product = Product.query.get_or_404(id)
    if not product.is_active:
        flash('商品不存在或已下架', 'error')
        return redirect(url_for('main.products'))

    # 获取商品属性（按组分类）
    attributes_by_group = product.get_attributes_by_group()

    # 获取商品所属分类的所有属性组（包含自定义输入配置）
    attribute_groups_with_config = []
    if product.category_id:
        category = Category.query.get(product.category_id)
        if category:
            # 获取该分类的属性组（直接关系）
            bound_groups = category.attribute_groups.filter_by(is_active=True).order_by(AttributeGroup.sort_order, AttributeGroup.name).all()
            for group in bound_groups:
                # 获取该组的属性
                group_attributes = Attribute.query.filter_by(group_id=group.id, is_active=True).order_by(Attribute.sort_order, Attribute.name).all()

                # 只包含有属性的组，或者启用了自定义输入的组
                if group_attributes or group.allow_custom_input:
                    attribute_groups_with_config.append({
                        'group': group,
                        'attributes': group_attributes,
                        'allow_custom_input': group.allow_custom_input,
                        'custom_input_type': group.custom_input_type,
                        'custom_input_label': group.custom_input_label,
                        'custom_input_placeholder': group.custom_input_placeholder,
                        'custom_validation_pattern': group.custom_validation_pattern,
                        'custom_validation_message': group.custom_validation_message
                    })

    # 获取数量折扣信息
    quantity_discounts = product.get_all_quantity_discounts()

    # 获取所有分类用于左侧面板
    categories = Category.query.filter_by(is_active=True).order_by(Category.sort_order).all()

    return render_template('main/product_detail.html',
                          product=product,
                          attributes_by_group=attributes_by_group,
                          attribute_groups_with_config=attribute_groups_with_config,
                          quantity_discounts=quantity_discounts,
                          categories=categories)

@main_bp.route('/demo/custom-attributes')
def custom_attributes_demo():
    """自定义属性功能演示"""
    return render_template('demo/custom_attributes.html')

@main_bp.route('/demo/address-selector')
def address_selector_demo():
    """重新设计的地址选择器演示"""
    return render_template('address_selector_demo.html')

@main_bp.route('/api/calculate-price', methods=['POST'])
def calculate_price():
    """价格计算API - 支持公式计算"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'error': '请求数据为空'
            }), 400
            
        product_id = data.get('product_id')
        quantity = data.get('quantity', 1)
        # 支持两种格式: selected_attributes (数组) 和 attributes (字典)
        selected_attributes = data.get('selected_attributes', [])
        attributes_dict = data.get('attributes', {})
        # 属性自定义数量 - 格式: {attr_id: custom_quantity}
        attribute_quantities = data.get('attribute_quantities', {})
        
        if not product_id:
            return jsonify({
                'error': '缺少商品ID'
            }), 400
        
        # 获取商品
        product = Product.query.get(product_id)
        if not product:
            return jsonify({
                'error': '商品不存在'
            }), 404
        
        # 基础价格
        base_price = float(product.base_price)
        
        # 计算属性价格修正
        attribute_modifier = 0
        attribute_details = []  # 存储属性计算详情
        
        # 处理 selected_attributes 数组格式 (前台主要使用格式)
        if selected_attributes:
            for attr_id in selected_attributes:
                # 检查是否是自定义属性组输入
                if isinstance(attr_id, str) and attr_id.startswith('custom_'):
                    # 处理自定义属性组输入
                    group_key = attr_id.replace('custom_', '')
                    custom_value_key = f'custom_group_{group_key}'
                    custom_value = attribute_quantities.get(custom_value_key)

                    if custom_value:
                        # 查找对应的属性组
                        group_name = group_key.replace('_', ' ')
                        group = AttributeGroup.query.filter_by(name=group_name).first()

                        if group and group.allow_custom_input:
                            attr_price = group.calculate_custom_price(custom_value, base_price)
                            calculation_detail = {
                                'id': attr_id,
                                'name': f'{group.name} (自定义)',
                                'value': str(custom_value),
                                'type': 'custom_group',
                                'custom_value': custom_value,
                                'calculated_price': attr_price
                            }

                            if group.custom_price_modifier_type == 'formula' and group.custom_price_formula:
                                calculation_detail.update({
                                    'formula': group.custom_price_formula,
                                    'formula_type': 'custom_group'
                                })

                            attribute_modifier += attr_price
                            attribute_details.append(calculation_detail)
                else:
                    # 处理标准属性
                    attr = Attribute.query.get(attr_id)
                    if attr:
                        attr_price = 0
                        calculation_detail = {
                            'id': attr_id,
                            'name': attr.name,
                            'value': attr.value,
                            'type': attr.get_calculation_type()
                        }

                        if attr.is_quantity_based and attr.price_formula:
                            # 公式计算
                            custom_quantity = attribute_quantities.get(str(attr_id), 1)
                            attr_price = attr.calculate_formula_price(custom_quantity, base_price)
                            calculation_detail.update({
                                'formula': attr.price_formula,
                                'custom_quantity': custom_quantity,
                                'unit': attr.quantity_unit,
                                'calculated_price': attr_price
                            })
                        elif attr.price_modifier_type == 'percentage':
                            # 百分比调整：如果基础价格为0，则百分比调整也为0
                            if base_price > 0:
                                attr_price = base_price * (float(attr.price_modifier) / 100)
                            calculation_detail.update({
                                'modifier': float(attr.price_modifier),
                                'calculated_price': attr_price
                            })
                        else:
                            # 固定金额调整：直接加上调整值
                            attr_price = float(attr.price_modifier)
                            calculation_detail.update({
                                'modifier': float(attr.price_modifier),
                                'calculated_price': attr_price
                            })

                        attribute_modifier += attr_price
                        attribute_details.append(calculation_detail)
        
        # 处理 attributes 字典格式 (兼容旧格式)
        elif attributes_dict:
            for attr_name, attr_value in attributes_dict.items():
                # 查找属性
                attr = Attribute.query.join(AttributeGroup).filter(
                    AttributeGroup.category_id == product.category_id,
                    Attribute.name == attr_name,
                    Attribute.value == attr_value
                ).first()
                
                if attr:
                    attr_price = 0
                    if attr.is_quantity_based and attr.price_formula:
                        # 公式计算
                        custom_quantity = attribute_quantities.get(str(attr.id), 1)
                        attr_price = attr.calculate_formula_price(custom_quantity, base_price)
                    elif attr.price_modifier_type == 'percentage':
                        # 百分比调整：如果基础价格为0，则百分比调整也为0
                        if base_price > 0:
                            attr_price = base_price * (float(attr.price_modifier) / 100)
                    else:
                        # 固定金额调整：直接加上调整值
                        attr_price = float(attr.price_modifier)
                    
                    attribute_modifier += attr_price
        
        # 单价（确保不小于0）
        unit_price = max(0, base_price + attribute_modifier)
        
        # 数量折扣
        discount_rate = 0
        applied_discount = None
        discounts = QuantityDiscount.query.filter(
            QuantityDiscount.product_id == product_id,
            QuantityDiscount.min_quantity <= quantity,
            QuantityDiscount.is_active == True
        ).order_by(QuantityDiscount.min_quantity.desc()).all()
        
        for discount in discounts:
            if discount.max_quantity is None or quantity <= discount.max_quantity:
                applied_discount = discount
                if discount.discount_type == 'percentage':
                    discount_rate = float(discount.discount_value) / 100
                else:
                    # 固定金额折扣转换为百分比
                    if unit_price > 0:
                        discount_rate = min(float(discount.discount_value) / unit_price, 1.0)
                break
        
        # 计算最终价格
        discounted_unit_price = unit_price * (1 - discount_rate)
        total_price = discounted_unit_price * quantity
        
        # 格式化价格为5位小数
        result = {
            'total_price': f"{total_price:.5f}",
            'unit_price': f"{unit_price:.5f}",
            'base_price': f"{base_price:.5f}",
            'attribute_modifier': f"{attribute_modifier:.5f}",
            'quantity': quantity,
            'attribute_details': attribute_details  # 新增：属性计算详情
        }
        
        # 添加折扣信息
        if applied_discount:
            result['discount'] = {
                'type': applied_discount.discount_type,
                'value': f"{applied_discount.discount_value:.5f}",
                'rate': f"{discount_rate:.5f}",
                'display': applied_discount.get_discount_display()
            }
        
        return jsonify(result)
        
    except Exception as e:
        print(f"价格计算API错误: {str(e)}")  # 调试信息
        import traceback
        traceback.print_exc()
        return jsonify({
            'error': f'价格计算失败: {str(e)}'
        }), 400

@main_bp.route('/orders')
@login_required
def orders():
    """我的订单"""
    page = request.args.get('page', 1, type=int)
    orders = current_user.orders.order_by(Order.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    return render_template('main/orders.html', orders=orders)

@main_bp.route('/order/<int:id>')
@login_required
def order_detail(id):
    """订单详情"""
    from models.order import Order
    order = Order.query.filter_by(id=id, user_id=current_user.id).first_or_404()
    return render_template('main/order_detail.html', order=order)

@main_bp.route('/api/orders/<int:id>/cancel', methods=['POST'])
@login_required
def cancel_order(id):
    """取消订单"""
    try:
        order = Order.query.filter_by(id=id, user_id=current_user.id).first_or_404()
        
        # 检查订单是否可以取消
        if not order.can_cancel():
            return jsonify({
                'success': False,
                'message': f'订单状态为"{order.get_status_display()}"，无法取消'
            }), 400
        
        # 更新订单状态为已取消
        order.status = 'cancelled'
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '订单已成功取消'
        })
        
    except Exception as e:
        db.session.rollback()
        import traceback
        print(f"取消订单失败: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'取消订单失败: {str(e)}'
        }), 500

@main_bp.route('/create-order', methods=['POST'])
@login_required
def create_order():
    """创建订单"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据为空'}), 400
        
        # 提取订单数据
        product_id = data.get('product_id')
        quantity = data.get('quantity', 1)
        selected_attributes = data.get('selected_attributes', [])
        attribute_quantities = data.get('attribute_quantities', {})  # 添加获取自定义数量参数
        
        # 收货信息
        recipient_name = data.get('recipient_name', '').strip()
        recipient_phone = data.get('recipient_phone', '').strip()
        shipping_province = data.get('shipping_province', '').strip()
        shipping_city = data.get('shipping_city', '').strip()
        shipping_district = data.get('shipping_district', '').strip()
        shipping_address = data.get('shipping_address', '').strip()
        shipping_postal_code = data.get('shipping_postal_code', '').strip()
        notes = data.get('notes', '').strip()
        
        # 验证必填字段（区县字段特殊处理）
        if not all([product_id, quantity, recipient_name, recipient_phone,
                   shipping_province, shipping_city, shipping_address]):
            return jsonify({'error': '请填写完整的商品信息和收货地址'}), 400

        # 区县字段特殊处理：如果为空，设置为空格
        if not shipping_district or shipping_district.strip() == '':
            shipping_district = ' '
        
        # 验证手机号格式
        import re
        phone_pattern = r'^1[3-9]\d{9}$'
        if not re.match(phone_pattern, recipient_phone):
            return jsonify({'error': '请输入正确的手机号码格式'}), 400
        
        # 获取商品信息
        product = Product.query.get(product_id)
        if not product or not product.is_active:
            return jsonify({'error': '商品不存在或已下架'}), 404
        
        # 验证数量
        if quantity < product.min_quantity or quantity > product.max_quantity:
            return jsonify({
                'error': f'数量必须在{product.min_quantity}-{product.max_quantity}之间'
            }), 400
        
        # 计算价格
        price_response = calculate_price_internal(product_id, quantity, selected_attributes, attribute_quantities)  # 传递自定义数量参数
        if 'error' in price_response:
            return jsonify({'error': price_response['error']}), 400
        
        unit_price = Decimal(str(price_response['unit_price']))
        total_price = Decimal(str(price_response['total_price']))
        
        # 计算运费（暂时包含在总价中，因为数据库没有单独的运费字段）
        free_shipping_amount = Decimal(str(SystemConfig.get_config('free_shipping_amount', 200)))
        default_shipping_fee = Decimal(str(SystemConfig.get_config('default_shipping_fee', 15)))
        shipping_fee = Decimal('0') if total_price >= free_shipping_amount else default_shipping_fee
        
        # 最终金额
        final_amount = total_price + shipping_fee
        
        # 生成订单号
        order_no = f"ORDER{uuid.uuid4().hex[:8].upper()}{current_user.id:04d}"
        
        # 创建订单
        order = Order(
            order_no=order_no,
            user_id=current_user.id,
            total_amount=total_price,
            final_amount=final_amount,
            shipping_name=recipient_name,
            shipping_phone=recipient_phone,
            notes=notes,
            status='pending'
        )
        
        # 设置完整地址
        order.set_full_address(
            shipping_province, 
            shipping_city, 
            shipping_district, 
            shipping_address, 
            shipping_postal_code
        )
        
        db.session.add(order)
        db.session.flush()  # 获取订单ID
        
        # 准备商品属性信息 - 使用OrderedDict确保顺序
        product_attributes = OrderedDict()

        if selected_attributes:
            # 分离标准属性和自定义属性组
            standard_attr_ids = []
            custom_groups = []

            for attr_id in selected_attributes:
                if isinstance(attr_id, str) and attr_id.startswith('custom_'):
                    custom_groups.append(attr_id)
                else:
                    standard_attr_ids.append(attr_id)

            # 处理标准属性
            if standard_attr_ids:
                attrs_with_groups = db.session.query(
                    Attribute, AttributeGroup
                ).join(
                    AttributeGroup,
                    Attribute.group_id == AttributeGroup.id
                ).filter(
                    Attribute.id.in_(standard_attr_ids)
                ).order_by(
                    AttributeGroup.sort_order,  # 先按属性组排序
                    AttributeGroup.name,        # 然后按属性组名称排序
                    Attribute.sort_order,       # 最后按属性排序
                    Attribute.name
                ).all()

                for attr, group in attrs_with_groups:
                    group_name = group.name if group else '属性'
                    attr_value = attr.value

                    # 如果是支持自定义数量的属性，添加数量信息
                    if attr.is_quantity_based and str(attr.id) in attribute_quantities:
                        custom_quantity = attribute_quantities[str(attr.id)]
                        attr_value = f"{attr_value} ({custom_quantity}{attr.quantity_unit})"

                    product_attributes[group_name] = attr_value

            # 处理自定义属性组
            for custom_attr_id in custom_groups:
                group_key = custom_attr_id.replace('custom_', '')
                custom_value_key = f'custom_group_{group_key}'
                custom_value = attribute_quantities.get(custom_value_key)

                if custom_value:
                    group_name = group_key.replace('_', ' ')
                    group = AttributeGroup.query.filter_by(name=group_name).first()

                    if group and group.allow_custom_input:
                        display_value = f"{custom_value} (自定义)"
                        product_attributes[group_name] = display_value
        
        # 创建订单项
        order_item = OrderItem(
            order_id=order.id,
            product_id=product_id,
            product_name=product.name,
            product_attributes=product_attributes,
            quantity=quantity,
            unit_price=unit_price,
            total_price=total_price
        )
        
        db.session.add(order_item)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '订单创建成功',
            'order_id': order.id,
            'order_no': order.order_no,
            'total_amount': float(final_amount),
            'redirect_url': url_for('main.order_detail', id=order.id)
        })
        
    except Exception as e:
        db.session.rollback()
        import traceback
        print(f"创建订单失败: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': f'创建订单失败: {str(e)}'}), 500

def calculate_price_internal(product_id, quantity, selected_attributes, attribute_quantities=None):
    """内部价格计算函数"""
    try:
        # 获取商品
        product = Product.query.get(product_id)
        if not product:
            return {'error': '商品不存在'}
        
        # 确保attribute_quantities有值
        if attribute_quantities is None:
            attribute_quantities = {}
            
        # 基础价格
        base_price = float(product.base_price)
        
        # 计算属性价格修正
        attribute_modifier = 0
        if selected_attributes:
            for attr_id in selected_attributes:
                # 检查是否是自定义属性组输入
                if isinstance(attr_id, str) and attr_id.startswith('custom_'):
                    # 处理自定义属性组输入
                    group_key = attr_id.replace('custom_', '')
                    custom_value_key = f'custom_group_{group_key}'
                    custom_value = attribute_quantities.get(custom_value_key)

                    if custom_value:
                        # 查找对应的属性组
                        group_name = group_key.replace('_', ' ')
                        group = AttributeGroup.query.filter_by(name=group_name).first()

                        if group and group.allow_custom_input:
                            attr_price = group.calculate_custom_price(custom_value, base_price)
                            attribute_modifier += attr_price
                else:
                    # 处理标准属性
                    attr = Attribute.query.get(attr_id)
                    if attr:
                        if attr.is_quantity_based and attr.price_formula:
                            # 公式计算 - 使用用户自定义数量
                            custom_quantity = attribute_quantities.get(str(attr_id), 1)
                            attribute_modifier += attr.calculate_formula_price(custom_quantity, base_price)
                        elif attr.price_modifier_type == 'percentage':
                            # 百分比调整：如果基础价格为0，则百分比调整也为0
                            if base_price > 0:
                                attribute_modifier += base_price * (float(attr.price_modifier) / 100)
                        else:
                            # 固定金额调整：直接加上调整值
                            attribute_modifier += float(attr.price_modifier)
        
        # 单价
        unit_price = max(0, base_price + attribute_modifier)
        
        # 数量折扣
        discount_rate = 0
        discounts = QuantityDiscount.query.filter(
            QuantityDiscount.product_id == product_id,
            QuantityDiscount.min_quantity <= quantity,
            QuantityDiscount.is_active == True
        ).order_by(QuantityDiscount.min_quantity.desc()).all()
        
        if discounts:
            for discount in discounts:
                if discount.max_quantity is None or quantity <= discount.max_quantity:
                    if discount.discount_type == 'percentage':
                        discount_rate = float(discount.discount_value) / 100
                    else:
                        # 固定金额折扣
                        discount_amount = float(discount.discount_value)
                        discount_rate = min(discount_amount / (unit_price * quantity), 1.0)
                    break
        
        # 总价
        total_price = unit_price * quantity * (1 - discount_rate)
        
        return {
            'unit_price': unit_price,
            'total_price': total_price,
            'discount_rate': discount_rate
        }
        
    except Exception as e:
        return {'error': f'价格计算失败: {str(e)}'}


@main_bp.route('/cart')
@login_required  
def cart():
    """购物车页面重定向到商品页面"""
    flash('购物车功能已停用，已为您跳转到商品页面', 'info')
    return redirect(url_for('main.products'))

@main_bp.route('/api/orders/<int:id>/pay', methods=['POST'])
@login_required
def pay_order(id):
    """支付订单"""
    try:
        # 获取订单
        order = Order.query.filter_by(id=id, user_id=current_user.id).first_or_404()
        
        # 检查订单是否可以支付
        if not order.can_pay():
            return jsonify({
                'success': False,
                'message': f'订单状态为"{order.get_status_display()}"，无法支付'
            }), 400
        
        # 获取支付方式
        data = request.get_json()
        payment_type = data.get('payment_type', 'alipay')
        
        # 支持的支付方式
        supported_types = PaymentService.get_config('types', 'alipay,wxpay').split(',')
        if payment_type not in supported_types:
            return jsonify({
                'success': False,
                'message': f'不支持的支付方式: {payment_type}'
            }), 400
        
        # 创建支付URL
        payment_url = PaymentService.create_payment_url(order, payment_type)
        
        return jsonify({
            'success': True,
            'message': '正在跳转到支付页面',
            'payment_url': payment_url
        })
        
    except Exception as e:
        current_app.logger.error(f"支付订单失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'message': f'支付订单失败: {str(e)}'
        }), 500

@main_bp.route('/payment/notify', methods=['POST'])
def payment_notify():
    """支付异步通知处理"""
    try:
        # 获取通知数据
        data = request.form.to_dict()
        
        # 验证签名
        sign = data.get('sign', '')
        if not PaymentService.verify_payment_notification(data, sign):
            current_app.logger.error("支付通知签名验证失败")
            return 'fail'
        
        # 处理支付结果
        trade_status = data.get('trade_status')
        out_trade_no = data.get('out_trade_no')
        
        # 查找订单
        order = Order.query.filter_by(order_no=out_trade_no).first()
        if not order:
            current_app.logger.error(f"未找到订单: {out_trade_no}")
            return 'fail'
        
        # 更新订单状态
        if trade_status == 'TRADE_SUCCESS':
            # 支付成功
            if order.status == 'pending':
                order.status = 'paid'
                order.payment_status = 'paid'
                order.payment_method = data.get('type', '')
                db.session.commit()
                current_app.logger.info(f"订单 {out_trade_no} 支付成功")
        
        return 'success'
        
    except Exception as e:
        current_app.logger.error(f"处理支付通知失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return 'fail'

@main_bp.route('/payment/<int:order_id>')
@login_required
def payment_page(order_id):
    """支付页面"""
    # 获取订单
    order = Order.query.filter_by(id=order_id, user_id=current_user.id).first_or_404()

    # 检查订单是否可以支付
    if not order.can_pay():
        flash(f'订单状态为"{order.get_status_display()}"，无法支付', 'error')
        return redirect(url_for('main.order_detail', id=order.id))

    # 获取支付方式
    payment_service = PaymentService()
    payment_types = payment_service.get_payment_types()

    return render_template('main/payment.html', order=order, payment_types=payment_types)

@main_bp.route('/payment/success')
@login_required
def payment_success():
    """支付成功页面"""
    order_id = request.args.get('order_id', type=int)

    if not order_id:
        flash('订单ID不能为空', 'error')
        return redirect(url_for('main.orders'))

    # 获取订单
    order = Order.query.filter_by(id=order_id, user_id=current_user.id).first_or_404()

    # 如果订单已支付，显示支付成功页面
    if order.payment_status == 'paid':
        return render_template('main/payment_success.html', order=order)

    # 查询订单支付状态
    result = PaymentService.query_order_status(order.order_no)

    if result.get('code') == 1 and result.get('status') == 1:
        # 支付成功，更新订单状态
        order.status = 'paid'
        order.payment_status = 'paid'
        db.session.commit()
        return render_template('main/payment_success.html', order=order)
    
    # 支付未完成，跳转到订单详情页
    flash('支付未完成，请重新支付', 'warning')
    return redirect(url_for('main.order_detail', id=order_id))

@main_bp.route('/api/regions/provinces')
def get_provinces():
    """获取所有省份数据"""
    try:
        # 首先尝试从数据库获取
        provinces = Region.get_provinces()

        # 如果数据库中没有数据，从JSON文件加载
        if not provinces:
            provinces = load_provinces_from_json()

        result = [{
            'id': p.id if hasattr(p, 'id') else p.get('id'),
            'name': p.name if hasattr(p, 'name') else p.get('name')
        } for p in provinces]

        # 确保中文字符正确编码
        response = jsonify(result)
        response.headers['Content-Type'] = 'application/json; charset=utf-8'
        return response
    except Exception as e:
        print(f"获取省份数据错误: {str(e)}")
        return jsonify({'error': '获取省份数据失败'}), 500

def load_provinces_from_json():
    """从JSON文件加载省份数据"""
    import os
    import json

    provinces = []

    # 添加测试省份
    provinces.append({
        'id': '999000',
        'name': '测试省（含无区县城市）'
    })

    # 定义省份文件映射
    province_files = {
        '320000': {'file': 'JS2025.json', 'name': '江苏省'},
        '330000': {'file': 'ZJ2025.json', 'name': '浙江省'},
        '310000': {'file': 'SH2025.json', 'name': '上海市'},
        '440000': {'file': 'GD2025.json', 'name': '广东省'},
        '430000': {'file': 'hunan2025.json', 'name': '湖南省'},
    }

    for province_id, info in province_files.items():
        json_path = os.path.join('chengshi', info['file'])
        if os.path.exists(json_path):
            try:
                with open(json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                provinces.append({
                    'id': province_id,
                    'name': data.get('name', info['name'])
                })
            except Exception as e:
                print(f"加载省份文件 {info['file']} 失败: {str(e)}")
                # 使用默认名称
                provinces.append({
                    'id': province_id,
                    'name': info['name']
                })

    return provinces

@main_bp.route('/api/regions/cities/<province_id>')
def get_cities(province_id):
    """获取指定省份的城市数据"""
    try:
        # 首先尝试从数据库获取
        cities = Region.get_cities(province_id)

        # 如果数据库中没有数据，尝试从JSON文件加载
        if not cities:
            cities = load_cities_from_json(province_id)

        result = [{
            'id': c.id if hasattr(c, 'id') else c.get('id'),
            'name': c.name if hasattr(c, 'name') else c.get('name')
        } for c in cities]

        # 确保中文字符正确编码
        response = jsonify(result)
        response.headers['Content-Type'] = 'application/json; charset=utf-8'
        return response
    except Exception as e:
        print(f"获取城市数据错误: {str(e)}")
        return jsonify({'error': '获取城市数据失败'}), 500

def load_cities_from_json(province_id):
    """从JSON文件加载城市数据"""
    import os
    import json

    # 添加测试省份，包含一些没有区县数据的城市
    if province_id == '999000':
        return [
            {'id': '999999', 'name': '测试市A（无区县）'},
            {'id': '888888', 'name': '测试市B（无区县）'},
            {'id': '777777', 'name': '测试市C（无区县）'},
        ]

    # 根据省份ID确定JSON文件
    province_mapping = {
        '320000': 'JS2025.json',    # 江苏省
        '330000': 'ZJ2025.json',    # 浙江省
        '310000': 'SH2025.json',    # 上海市
        '440000': 'GD2025.json',    # 广东省
        '430000': 'hunan2025.json', # 湖南省
    }

    json_file = province_mapping.get(province_id)

    if not json_file:
        return []

    json_path = os.path.join('chengshi', json_file)
    if not os.path.exists(json_path):
        return []

    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 返回城市数据
        cities = []
        for city in data.get('citys', []):
            cities.append({
                'id': city.get('code', '')[:6],  # 取前6位作为城市代码
                'name': city.get('name', '')
            })
        return cities

    except Exception as e:
        print(f"从JSON文件加载城市数据失败: {str(e)}")
        return []

@main_bp.route('/api/regions/districts/<city_id>')
def get_districts(city_id):
    """获取指定城市的区县数据"""
    try:
        # 模拟一些城市没有区县数据（用于测试）
        test_cities_without_districts = ['999999', '888888', '777777']

        if city_id in test_cities_without_districts:
            # 返回空数组，模拟没有区县数据的情况
            response = jsonify([])
            response.headers['Content-Type'] = 'application/json; charset=utf-8'
            return response

        # 首先尝试从数据库获取
        districts = Region.get_districts(city_id)

        # 如果数据库中没有数据，尝试从JSON文件加载
        if not districts:
            districts = load_districts_from_json(city_id)

        result = [{
            'id': d.id if hasattr(d, 'id') else d.get('id'),
            'name': d.name if hasattr(d, 'name') else d.get('name')
        } for d in districts]

        # 确保中文字符正确编码
        response = jsonify(result)
        response.headers['Content-Type'] = 'application/json; charset=utf-8'
        return response
    except Exception as e:
        print(f"获取区县数据错误: {str(e)}")
        return jsonify({'error': '获取区县数据失败'}), 500

def load_districts_from_json(city_id):
    """从JSON文件加载区县数据"""
    import os
    import json

    # 根据城市ID确定省份
    province_mapping = {
        '320': 'JS2025.json',    # 江苏省
        '330': 'ZJ2025.json',    # 浙江省
        '310': 'SH2025.json',    # 上海市
        '440': 'GD2025.json',    # 广东省
        '430': 'hunan2025.json', # 湖南省
    }

    province_code = city_id[:3]
    json_file = province_mapping.get(province_code)

    if not json_file:
        return []

    json_path = os.path.join('chengshi', json_file)
    if not os.path.exists(json_path):
        return []

    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 查找对应的城市
        for city in data.get('citys', []):
            # 匹配城市代码（去掉后面的000）
            city_code = city.get('code', '')[:6]
            if city_code == city_id:
                # 返回区县数据
                areas = city.get('areas', [])
                districts = []
                for area in areas:
                    districts.append({
                        'id': area.get('code', '')[:9],  # 取前9位作为区县代码
                        'name': area.get('name', '')
                    })
                return districts

        return []
    except Exception as e:
        print(f"从JSON文件加载区县数据失败: {str(e)}")
        return []

@main_bp.route('/test-form')
def test_form():
    """测试表单页面"""
    return render_template('test_form.html')

@main_bp.route('/debug-regions')
def debug_regions():
    """区域联动调试页面"""
    return render_template('debug_regions.html')

# ========== 地址管理相关路由 ==========

@main_bp.route('/addresses')
@login_required
def addresses():
    """用户地址管理页面"""
    from models.user import UserAddress
    addresses = current_user.addresses.order_by(UserAddress.is_default.desc(), UserAddress.created_at.desc()).all()
    return render_template('user/addresses.html', addresses=addresses)

@main_bp.route('/api/addresses', methods=['POST'])
@login_required
def create_address():
    """创建新地址"""
    try:
        from models.user import UserAddress
        data = request.get_json()

        # 验证必填字段（区县字段特殊处理）
        required_fields = ['recipient_name', 'phone', 'province', 'city', 'address_detail']
        for field in required_fields:
            if not data.get(field, '').strip():
                return jsonify({
                    'success': False,
                    'message': f'请填写{field}'
                }), 400

        # 区县字段特殊处理：如果为空，设置为空格
        district = data.get('district', '').strip()
        if not district:
            data['district'] = ' '

        # 验证手机号格式
        import re
        phone_pattern = r'^1[3-9]\d{9}$'
        if not re.match(phone_pattern, data.get('phone', '')):
            return jsonify({
                'success': False,
                'message': '请输入正确的手机号码格式'
            }), 400

        # 创建地址
        address = UserAddress(
            user_id=current_user.id,
            recipient_name=data['recipient_name'].strip(),
            phone=data['phone'].strip(),
            province=data['province'].strip(),
            city=data['city'].strip(),
            district=data['district'].strip(),
            address_detail=data['address_detail'].strip(),
            postal_code=data.get('postal_code', '').strip(),
            is_default=data.get('is_default', False)
        )

        # 如果设置为默认地址，先取消其他默认地址
        if address.is_default:
            UserAddress.query.filter_by(user_id=current_user.id, is_default=True).update({'is_default': False})

        db.session.add(address)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '地址添加成功',
            'address_id': address.id
        })

    except Exception as e:
        db.session.rollback()
        print(f"创建地址失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'创建地址失败: {str(e)}'
        }), 500

@main_bp.route('/api/addresses/<int:address_id>')
@login_required
def get_address(address_id):
    """获取单个地址信息"""
    try:
        from models.user import UserAddress
        address = UserAddress.query.filter_by(id=address_id, user_id=current_user.id).first()

        if not address:
            return jsonify({
                'success': False,
                'message': '地址不存在'
            }), 404

        return jsonify({
            'success': True,
            'address': {
                'id': address.id,
                'recipient_name': address.recipient_name,
                'phone': address.phone,
                'province': address.province,
                'city': address.city,
                'district': address.district,
                'address_detail': address.address_detail,
                'postal_code': address.postal_code,
                'is_default': address.is_default
            }
        })

    except Exception as e:
        print(f"获取地址失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取地址失败: {str(e)}'
        }), 500

@main_bp.route('/api/addresses/<int:address_id>', methods=['PUT'])
@login_required
def update_address(address_id):
    """更新地址信息"""
    try:
        from models.user import UserAddress
        address = UserAddress.query.filter_by(id=address_id, user_id=current_user.id).first()

        if not address:
            return jsonify({
                'success': False,
                'message': '地址不存在'
            }), 404

        data = request.get_json()

        # 验证必填字段
        required_fields = ['recipient_name', 'phone', 'province', 'city', 'district', 'address_detail']
        for field in required_fields:
            if not data.get(field, '').strip():
                return jsonify({
                    'success': False,
                    'message': f'请填写{field}'
                }), 400

        # 验证手机号格式
        import re
        phone_pattern = r'^1[3-9]\d{9}$'
        if not re.match(phone_pattern, data.get('phone', '')):
            return jsonify({
                'success': False,
                'message': '请输入正确的手机号码格式'
            }), 400

        # 更新地址信息
        address.recipient_name = data['recipient_name'].strip()
        address.phone = data['phone'].strip()
        address.province = data['province'].strip()
        address.city = data['city'].strip()
        address.district = data['district'].strip()
        address.address_detail = data['address_detail'].strip()
        address.postal_code = data.get('postal_code', '').strip()

        # 处理默认地址设置
        is_default = data.get('is_default', False)
        if is_default and not address.is_default:
            # 设置为默认地址，先取消其他默认地址
            UserAddress.query.filter_by(user_id=current_user.id, is_default=True).update({'is_default': False})
            address.is_default = True
        elif not is_default and address.is_default:
            # 取消默认地址
            address.is_default = False

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '地址更新成功'
        })

    except Exception as e:
        db.session.rollback()
        print(f"更新地址失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新地址失败: {str(e)}'
        }), 500

@main_bp.route('/api/addresses/<int:address_id>', methods=['DELETE'])
@login_required
def delete_address(address_id):
    """删除地址"""
    try:
        from models.user import UserAddress
        address = UserAddress.query.filter_by(id=address_id, user_id=current_user.id).first()

        if not address:
            return jsonify({
                'success': False,
                'message': '地址不存在'
            }), 404

        # 不允许删除默认地址（如果是唯一地址除外）
        if address.is_default:
            address_count = UserAddress.query.filter_by(user_id=current_user.id).count()
            if address_count > 1:
                return jsonify({
                    'success': False,
                    'message': '不能删除默认地址，请先设置其他地址为默认地址'
                }), 400

        db.session.delete(address)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '地址删除成功'
        })

    except Exception as e:
        db.session.rollback()
        print(f"删除地址失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除地址失败: {str(e)}'
        }), 500

@main_bp.route('/api/addresses/<int:address_id>/set-default', methods=['POST'])
@login_required
def set_default_address(address_id):
    """设置默认地址"""
    try:
        from models.user import UserAddress
        address = UserAddress.query.filter_by(id=address_id, user_id=current_user.id).first()

        if not address:
            return jsonify({
                'success': False,
                'message': '地址不存在'
            }), 404

        # 先取消其他默认地址
        UserAddress.query.filter_by(user_id=current_user.id, is_default=True).update({'is_default': False})

        # 设置当前地址为默认
        address.is_default = True
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '默认地址设置成功'
        })

    except Exception as e:
        db.session.rollback()
        print(f"设置默认地址失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'设置默认地址失败: {str(e)}'
        }), 500

@main_bp.route('/init-regions')
def init_regions():
    """初始化基础区域数据"""
    try:
        # 检查是否已有数据
        existing_count = Region.query.count()
        if existing_count > 0:
            return jsonify({
                'success': True,
                'message': f'数据库中已存在 {existing_count} 条区域数据，无需初始化'
            })

        # 省份数据
        provinces = [
            {'id': '110000', 'name': '北京市'},
            {'id': '120000', 'name': '天津市'},
            {'id': '310000', 'name': '上海市'},
            {'id': '440000', 'name': '广东省'},
            {'id': '320000', 'name': '江苏省'},
            {'id': '330000', 'name': '浙江省'},
        ]

        # 城市数据
        cities = [
            # 北京市
            {'id': '110100', 'name': '北京市', 'parent_id': '110000'},
            # 天津市
            {'id': '120100', 'name': '天津市', 'parent_id': '120000'},
            # 上海市
            {'id': '310100', 'name': '上海市', 'parent_id': '310000'},
            # 广东省
            {'id': '440100', 'name': '广州市', 'parent_id': '440000'},
            {'id': '440300', 'name': '深圳市', 'parent_id': '440000'},
            {'id': '440400', 'name': '珠海市', 'parent_id': '440000'},
            # 江苏省
            {'id': '320100', 'name': '南京市', 'parent_id': '320000'},
            {'id': '320200', 'name': '无锡市', 'parent_id': '320000'},
            {'id': '320500', 'name': '苏州市', 'parent_id': '320000'},
            # 浙江省
            {'id': '330100', 'name': '杭州市', 'parent_id': '330000'},
            {'id': '330200', 'name': '宁波市', 'parent_id': '330000'},
            {'id': '330300', 'name': '温州市', 'parent_id': '330000'},
        ]

        # 区县数据
        districts = [
            # 北京市区县
            {'id': '110101', 'name': '东城区', 'parent_id': '110100'},
            {'id': '110102', 'name': '西城区', 'parent_id': '110100'},
            {'id': '110105', 'name': '朝阳区', 'parent_id': '110100'},
            {'id': '110106', 'name': '丰台区', 'parent_id': '110100'},
            {'id': '110107', 'name': '石景山区', 'parent_id': '110100'},
            {'id': '110108', 'name': '海淀区', 'parent_id': '110100'},

            # 天津市区县
            {'id': '120101', 'name': '和平区', 'parent_id': '120100'},
            {'id': '120102', 'name': '河东区', 'parent_id': '120100'},
            {'id': '120103', 'name': '河西区', 'parent_id': '120100'},
            {'id': '120104', 'name': '南开区', 'parent_id': '120100'},
            {'id': '120105', 'name': '河北区', 'parent_id': '120100'},

            # 上海市区县
            {'id': '310101', 'name': '黄浦区', 'parent_id': '310100'},
            {'id': '310104', 'name': '徐汇区', 'parent_id': '310100'},
            {'id': '310105', 'name': '长宁区', 'parent_id': '310100'},
            {'id': '310106', 'name': '静安区', 'parent_id': '310100'},
            {'id': '310107', 'name': '普陀区', 'parent_id': '310100'},

            # 广州市区县
            {'id': '440103', 'name': '荔湾区', 'parent_id': '440100'},
            {'id': '440104', 'name': '越秀区', 'parent_id': '440100'},
            {'id': '440105', 'name': '海珠区', 'parent_id': '440100'},
            {'id': '440106', 'name': '天河区', 'parent_id': '440100'},
            {'id': '440111', 'name': '白云区', 'parent_id': '440100'},

            # 深圳市区县
            {'id': '440303', 'name': '罗湖区', 'parent_id': '440300'},
            {'id': '440304', 'name': '福田区', 'parent_id': '440300'},
            {'id': '440305', 'name': '南山区', 'parent_id': '440300'},
            {'id': '440306', 'name': '宝安区', 'parent_id': '440300'},
            {'id': '440307', 'name': '龙岗区', 'parent_id': '440300'},

            # 杭州市区县
            {'id': '330102', 'name': '上城区', 'parent_id': '330100'},
            {'id': '330105', 'name': '拱墅区', 'parent_id': '330100'},
            {'id': '330106', 'name': '西湖区', 'parent_id': '330100'},
            {'id': '330108', 'name': '滨江区', 'parent_id': '330100'},
            {'id': '330109', 'name': '萧山区', 'parent_id': '330100'},
        ]

        # 添加省份
        for idx, province in enumerate(provinces):
            region = Region(
                id=province['id'],
                name=province['name'],
                parent_id='0',
                level=1,
                sort_order=idx
            )
            db.session.add(region)

        # 添加城市
        for idx, city in enumerate(cities):
            region = Region(
                id=city['id'],
                name=city['name'],
                parent_id=city['parent_id'],
                level=2,
                sort_order=idx
            )
            db.session.add(region)

        # 添加区县
        for idx, district in enumerate(districts):
            region = Region(
                id=district['id'],
                name=district['name'],
                parent_id=district['parent_id'],
                level=3,
                sort_order=idx
            )
            db.session.add(region)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'成功添加基础区域数据：{len(provinces)} 个省份，{len(cities)} 个城市，{len(districts)} 个区县'
        })

    except Exception as e:
        db.session.rollback()
        print(f"初始化区域数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'初始化区域数据失败: {str(e)}'
        }), 500

@main_bp.route('/create-test-user')
def create_test_user():
    """创建测试用户"""
    try:
        from models.user import User

        # 检查是否已存在测试用户
        test_user = User.query.filter_by(username='testuser').first()
        if test_user:
            return jsonify({
                'success': True,
                'message': '测试用户已存在',
                'username': 'testuser',
                'password': 'test123'
            })

        # 创建测试用户
        test_user = User(
            username='testuser',
            email='<EMAIL>',
            real_name='测试用户',
            phone='13800138000',
            is_active=True
        )
        test_user.set_password('test123')

        db.session.add(test_user)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '测试用户创建成功',
            'username': 'testuser',
            'password': 'test123'
        })

    except Exception as e:
        db.session.rollback()
        print(f"创建测试用户失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'创建测试用户失败: {str(e)}'
        }), 500

@main_bp.route('/simple-region-test')
def simple_region_test():
    """简化版区域联动测试页面"""
    return render_template('simple_region_test.html')

@main_bp.route('/test-region-linkage')
def test_region_linkage():
    """测试区域联动功能"""
    return render_template('test_region_linkage.html')

@main_bp.route('/region-stats')
def region_stats():
    """区域数据统计页面"""
    try:
        # 统计数据
        provinces = Region.query.filter_by(level=1).all()
        cities = Region.query.filter_by(level=2).all()
        districts = Region.query.filter_by(level=3).all()

        # 按省份统计城市和区县数量
        province_stats = []
        for province in provinces:
            city_count = Region.query.filter_by(parent_id=province.id, level=2).count()

            # 统计该省份的区县数量
            district_count = 0
            province_cities = Region.query.filter_by(parent_id=province.id, level=2).all()
            for city in province_cities:
                district_count += Region.query.filter_by(parent_id=city.id, level=3).count()

            province_stats.append({
                'name': province.name,
                'id': province.id,
                'city_count': city_count,
                'district_count': district_count
            })

        # 按区县数量排序
        province_stats.sort(key=lambda x: x['district_count'], reverse=True)

        stats = {
            'total_provinces': len(provinces),
            'total_cities': len(cities),
            'total_districts': len(districts),
            'province_stats': province_stats
        }

        return render_template('region_stats.html', stats=stats)
    except Exception as e:
        print(f"获取区域统计失败: {str(e)}")
        return f"获取区域统计失败: {str(e)}", 500

@main_bp.route('/test-district-validation')
def test_district_validation():
    """测试区县验证功能"""
    return render_template('test_district_validation.html')

@main_bp.route('/test-attributes')
def test_attributes():
    """测试商品属性系统"""
    return render_template('test_attributes.html')

@main_bp.route('/admin/attributes')
def admin_attributes():
    """属性管理界面"""
    return render_template('admin/attribute_management.html')

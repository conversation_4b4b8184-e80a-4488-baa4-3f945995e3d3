<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>区县验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        select, input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        select:disabled, input:disabled {
            background-color: #f9f9f9;
            color: #999;
        }
        input[readonly] {
            background-color: #f8f9fa;
            cursor: not-allowed;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .result h3 {
            margin-top: 0;
            color: #007bff;
        }
        .result pre {
            background: #fff;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
        .notification {
            padding: 12px 20px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .notification.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .notification.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .notification.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .test-scenarios {
            margin-top: 30px;
            padding: 20px;
            background-color: #e7f3ff;
            border-radius: 4px;
        }
        .test-scenarios h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .test-scenarios ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .test-scenarios li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>区县验证测试</h1>
        
        <form id="testForm">
            <div class="form-group">
                <label for="province">省份 *</label>
                <select id="province" name="province" required>
                    <option value="">请选择省份</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="city">城市 *</label>
                <select id="city" name="city" required disabled>
                    <option value="">请选择城市</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="district">区县</label>
                <select id="district" name="district" disabled>
                    <option value="">请选择区县</option>
                </select>
                <input type="text" id="districtInput" name="districtInput" placeholder="请输入区县名称" style="display: none;" disabled>
            </div>
            
            <div class="form-group">
                <label for="address">详细地址 *</label>
                <input type="text" id="address" name="address" placeholder="请输入详细地址" required>
            </div>
            
            <button type="button" class="btn" onclick="validateForm()">验证表单</button>
            <button type="button" class="btn" onclick="submitForm()">提交表单</button>
        </form>
        
        <div id="notifications"></div>
        
        <div class="result">
            <h3>当前选择</h3>
            <pre id="resultDisplay">请选择省市区县...</pre>
        </div>
        
        <div class="test-scenarios">
            <h3>测试场景</h3>
            <ul>
                <li><strong>有区县数据的城市</strong>：选择广东省 → 广州市，应该显示区县选择框</li>
                <li><strong>无区县数据的城市</strong>：选择河北省 → 石家庄市，应该显示只读输入框，默认值为空格</li>
                <li><strong>验证逻辑</strong>：
                    <ul>
                        <li>有区县数据时：必须选择一个区县</li>
                        <li>无区县数据时：自动设置为空格，不需要用户操作</li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/simple-region-selector.js') }}"></script>
    <script>
        let regionSelector = null;

        // 初始化地址选择器
        function initRegionSelector() {
            try {
                regionSelector = new SimpleRegionSelector({
                    provinceSelector: '#province',
                    citySelector: '#city',
                    districtSelector: '#district',
                    districtInputSelector: '#districtInput',
                    provincePlaceholder: '请选择省份',
                    cityPlaceholder: '请选择城市',
                    districtPlaceholder: '请选择区县',
                    onChange: function(region) {
                        console.log('地址选择变化:', region);
                        updateResult(region);
                    }
                });
                console.log('地址选择器初始化成功');
            } catch (error) {
                console.error('地址选择器初始化失败:', error);
                showNotification('error', '地址选择器初始化失败: ' + error.message);
            }
        }

        // 更新结果显示
        function updateResult(region) {
            const resultDisplay = document.getElementById('resultDisplay');
            const result = {
                province: region.province,
                city: region.city,
                district: region.district,
                districtMode: region.districtMode,
                selectedData: regionSelector ? regionSelector.getSelectedRegion() : null
            };
            resultDisplay.textContent = JSON.stringify(result, null, 2);
        }

        // 显示通知
        function showNotification(type, message) {
            const notifications = document.getElementById('notifications');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            notifications.appendChild(notification);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

        // 验证表单
        function validateForm() {
            const province = document.getElementById('province').value;
            const city = document.getElementById('city').value;
            const district = document.getElementById('district');
            const districtInput = document.getElementById('districtInput');
            const address = document.getElementById('address').value;

            // 基本验证
            if (!province) {
                showNotification('warning', '请选择省份');
                return false;
            }
            if (!city) {
                showNotification('warning', '请选择城市');
                return false;
            }
            if (!address.trim()) {
                showNotification('warning', '请填写详细地址');
                return false;
            }

            // 区县验证
            let districtValue = '';
            if (district.style.display !== 'none' && !district.disabled) {
                // 选择框模式
                districtValue = district.value;
                if (!districtValue) {
                    showNotification('warning', '请选择区县');
                    return false;
                }
            } else if (districtInput.style.display !== 'none' && !districtInput.disabled) {
                // 输入框模式
                districtValue = districtInput.value.trim();
                if (!districtValue) {
                    districtInput.value = ' '; // 自动设置为空格
                    districtValue = ' ';
                }
            }

            showNotification('success', '表单验证通过！区县值: "' + districtValue + '"');
            return true;
        }

        // 提交表单
        function submitForm() {
            if (validateForm()) {
                showNotification('success', '表单提交成功！（模拟）');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initRegionSelector();
        });
    </script>
</body>
</html>

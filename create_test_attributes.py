#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建测试属性数据
为商品属性系统创建示例数据
"""

import os
import sys
sys.path.insert(0, os.path.abspath('.'))

from app import create_app
from models import db
from models.product import Product, AttributeGroup, Attribute
from models.order import ProductAttribute

def create_test_attributes():
    """创建测试属性数据"""
    app = create_app()
    
    with app.app_context():
        try:
            print("开始创建测试属性数据...")
            
            # 获取第一个产品（名片）
            product = Product.query.filter_by(name='名片').first()
            if not product:
                print("未找到名片产品，请先创建产品")
                return False
            
            print(f"为产品 '{product.name}' (ID: {product.id}) 创建属性...")
            
            # 1. 创建尺寸属性组
            size_group = AttributeGroup.query.filter_by(name='尺寸').first()
            if not size_group:
                size_group = AttributeGroup(
                    name='尺寸',
                    description='名片尺寸规格',
                    display_type='radio',
                    sort_order=1,
                    is_active=True
                )
                db.session.add(size_group)
                db.session.flush()  # 获取ID
                print("✅ 创建尺寸属性组")
            else:
                print("✅ 尺寸属性组已存在")
            
            # 尺寸属性
            size_attributes = [
                {
                    'name': '标准名片',
                    'value': '90×54mm',
                    'price_modifier': 0.00,
                    'price_modifier_type': 'fixed',
                    'input_type': 'radio',
                    'sort_order': 1
                },
                {
                    'name': '欧式名片',
                    'value': '85×54mm',
                    'price_modifier': 5.00,
                    'price_modifier_type': 'fixed',
                    'input_type': 'radio',
                    'sort_order': 2
                },
                {
                    'name': '方形名片',
                    'value': '54×54mm',
                    'price_modifier': 8.00,
                    'price_modifier_type': 'fixed',
                    'input_type': 'radio',
                    'sort_order': 3
                }
            ]
            
            for attr_data in size_attributes:
                existing_attr = Attribute.query.filter_by(
                    group_id=size_group.id,
                    name=attr_data['name']
                ).first()
                
                if not existing_attr:
                    attr = Attribute(
                        group_id=size_group.id,
                        **attr_data
                    )
                    db.session.add(attr)
                    db.session.flush()
                    
                    # 关联到产品
                    product_attr = ProductAttribute(
                        product_id=product.id,
                        attribute_id=attr.id,
                        is_required=True
                    )
                    db.session.add(product_attr)
                    print(f"  ✅ 创建尺寸属性: {attr_data['name']}")
                else:
                    print(f"  ✅ 尺寸属性已存在: {attr_data['name']}")
            
            # 2. 创建材质属性组
            material_group = AttributeGroup.query.filter_by(name='材质').first()
            if not material_group:
                material_group = AttributeGroup(
                    name='材质',
                    description='名片纸张材质',
                    display_type='select',
                    sort_order=2,
                    is_active=True
                )
                db.session.add(material_group)
                db.session.flush()
                print("✅ 创建材质属性组")
            else:
                print("✅ 材质属性组已存在")
            
            # 材质属性
            material_attributes = [
                {
                    'name': '300g铜版纸',
                    'value': '300g铜版纸',
                    'price_modifier': 0.00,
                    'price_modifier_type': 'fixed',
                    'input_type': 'select',
                    'sort_order': 1
                },
                {
                    'name': '350g艺术纸',
                    'value': '350g艺术纸',
                    'price_modifier': 10.00,
                    'price_modifier_type': 'fixed',
                    'input_type': 'select',
                    'sort_order': 2
                },
                {
                    'name': '400g特种纸',
                    'value': '400g特种纸',
                    'price_modifier': 20.00,
                    'price_modifier_type': 'fixed',
                    'input_type': 'select',
                    'sort_order': 3
                }
            ]
            
            for attr_data in material_attributes:
                existing_attr = Attribute.query.filter_by(
                    group_id=material_group.id,
                    name=attr_data['name']
                ).first()
                
                if not existing_attr:
                    attr = Attribute(
                        group_id=material_group.id,
                        **attr_data
                    )
                    db.session.add(attr)
                    db.session.flush()
                    
                    # 关联到产品
                    product_attr = ProductAttribute(
                        product_id=product.id,
                        attribute_id=attr.id,
                        is_required=True
                    )
                    db.session.add(product_attr)
                    print(f"  ✅ 创建材质属性: {attr_data['name']}")
                else:
                    print(f"  ✅ 材质属性已存在: {attr_data['name']}")
            
            # 3. 创建数量属性组
            quantity_group = AttributeGroup.query.filter_by(name='印刷数量').first()
            if not quantity_group:
                quantity_group = AttributeGroup(
                    name='印刷数量',
                    description='名片印刷数量',
                    display_type='radio',
                    sort_order=3,
                    is_active=True
                )
                db.session.add(quantity_group)
                db.session.flush()
                print("✅ 创建数量属性组")
            else:
                print("✅ 数量属性组已存在")
            
            # 数量属性
            quantity_attributes = [
                {
                    'name': '100张',
                    'value': '100张',
                    'price_modifier': 0.00,
                    'price_modifier_type': 'fixed',
                    'input_type': 'radio',
                    'sort_order': 1
                },
                {
                    'name': '200张',
                    'value': '200张',
                    'price_modifier': 15.00,
                    'price_modifier_type': 'fixed',
                    'input_type': 'radio',
                    'sort_order': 2
                },
                {
                    'name': '500张',
                    'value': '500张',
                    'price_modifier': 30.00,
                    'price_modifier_type': 'fixed',
                    'input_type': 'radio',
                    'sort_order': 3
                },
                {
                    'name': '1000张',
                    'value': '1000张',
                    'price_modifier': 50.00,
                    'price_modifier_type': 'fixed',
                    'input_type': 'radio',
                    'sort_order': 4
                }
            ]
            
            for attr_data in quantity_attributes:
                existing_attr = Attribute.query.filter_by(
                    group_id=quantity_group.id,
                    name=attr_data['name']
                ).first()
                
                if not existing_attr:
                    attr = Attribute(
                        group_id=quantity_group.id,
                        **attr_data
                    )
                    db.session.add(attr)
                    db.session.flush()
                    
                    # 关联到产品
                    product_attr = ProductAttribute(
                        product_id=product.id,
                        attribute_id=attr.id,
                        is_required=True
                    )
                    db.session.add(product_attr)
                    print(f"  ✅ 创建数量属性: {attr_data['name']}")
                else:
                    print(f"  ✅ 数量属性已存在: {attr_data['name']}")
            
            # 4. 创建自定义属性组
            custom_group = AttributeGroup.query.filter_by(name='个性化定制').first()
            if not custom_group:
                custom_group = AttributeGroup(
                    name='个性化定制',
                    description='个性化定制选项',
                    display_type='radio',
                    sort_order=4,
                    is_active=True
                )
                db.session.add(custom_group)
                db.session.flush()
                print("✅ 创建个性化定制属性组")
            else:
                print("✅ 个性化定制属性组已存在")
            
            # 自定义属性
            custom_attributes = [
                {
                    'name': '公司名称',
                    'value': '自定义输入',
                    'price_modifier': 0.00,
                    'price_modifier_type': 'fixed',
                    'input_type': 'text_input',
                    'is_custom_input': True,
                    'input_placeholder': '请输入公司名称',
                    'validation_pattern': r'^.{1,50}$',
                    'validation_message': '公司名称长度应在1-50个字符之间',
                    'sort_order': 1
                },
                {
                    'name': '联系电话',
                    'value': '自定义输入',
                    'price_modifier': 0.00,
                    'price_modifier_type': 'fixed',
                    'input_type': 'text_input',
                    'is_custom_input': True,
                    'input_placeholder': '请输入联系电话',
                    'validation_pattern': r'^1[3-9]\d{9}$',
                    'validation_message': '请输入正确的手机号码',
                    'sort_order': 2
                },
                {
                    'name': '特殊要求',
                    'value': '自定义输入',
                    'price_modifier': 0.00,
                    'price_modifier_type': 'fixed',
                    'input_type': 'textarea',
                    'is_custom_input': True,
                    'input_placeholder': '请输入特殊要求（可选）',
                    'sort_order': 3
                }
            ]
            
            for attr_data in custom_attributes:
                existing_attr = Attribute.query.filter_by(
                    group_id=custom_group.id,
                    name=attr_data['name']
                ).first()
                
                if not existing_attr:
                    attr = Attribute(
                        group_id=custom_group.id,
                        **attr_data
                    )
                    db.session.add(attr)
                    db.session.flush()
                    
                    # 关联到产品
                    product_attr = ProductAttribute(
                        product_id=product.id,
                        attribute_id=attr.id,
                        is_required=(attr_data['name'] != '特殊要求')  # 特殊要求不是必填
                    )
                    db.session.add(product_attr)
                    print(f"  ✅ 创建自定义属性: {attr_data['name']}")
                else:
                    print(f"  ✅ 自定义属性已存在: {attr_data['name']}")
            
            # 提交所有更改
            db.session.commit()
            print("\n✅ 测试属性数据创建完成！")
            
            # 显示创建的数据统计
            total_groups = AttributeGroup.query.count()
            total_attributes = Attribute.query.count()
            product_attributes = ProductAttribute.query.filter_by(product_id=product.id).count()
            
            print(f"\n📊 数据统计:")
            print(f"  属性组总数: {total_groups}")
            print(f"  属性总数: {total_attributes}")
            print(f"  产品关联属性数: {product_attributes}")
            
            return True
            
        except Exception as e:
            print(f"创建测试数据失败: {str(e)}")
            db.session.rollback()
            return False

if __name__ == '__main__':
    success = create_test_attributes()
    if success:
        print("\n🎉 测试数据创建成功！")
        print("现在可以访问 /test-attributes 页面测试属性系统")
    else:
        print("\n❌ 测试数据创建失败")
        sys.exit(1)

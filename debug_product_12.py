#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试产品12的自定义输入显示问题
"""

from app import create_app
from models import db
from models.product import AttributeGroup, Attribute, Category, Product

def debug_product_12():
    """调试产品12的配置"""
    
    app = create_app()
    
    with app.app_context():
        print("🔍 调试产品12的自定义输入显示问题...")
        
        # 1. 获取产品12的详细信息
        product = Product.query.get(12)
        if not product:
            print("❌ 产品12不存在")
            return
        
        print(f"\n🛍️ 产品信息:")
        print(f"  ID: {product.id}")
        print(f"  名称: {product.name}")
        print(f"  分类ID: {product.category_id}")
        print(f"  是否激活: {product.is_active}")
        
        # 2. 获取产品分类信息
        if product.category_id:
            category = Category.query.get(product.category_id)
            if category:
                print(f"\n📂 分类信息:")
                print(f"  ID: {category.id}")
                print(f"  名称: {category.name}")
                print(f"  是否激活: {category.is_active}")
                
                # 3. 获取分类的属性组
                groups = category.attribute_groups.filter_by(is_active=True).order_by(AttributeGroup.sort_order, AttributeGroup.name).all()
                print(f"\n🎛️ 分类的属性组 ({len(groups)} 个):")
                
                for group in groups:
                    print(f"  - {group.name} (ID: {group.id})")
                    print(f"    显示方式: {group.display_type}")
                    print(f"    允许自定义输入: {group.allow_custom_input}")
                    print(f"    自定义输入类型: {group.custom_input_type}")
                    print(f"    自定义标签: {group.custom_input_label}")
                    print(f"    自定义占位符: {group.custom_input_placeholder}")
                    
                    # 获取该组的属性
                    attributes = Attribute.query.filter_by(group_id=group.id, is_active=True).order_by(Attribute.sort_order, Attribute.name).all()
                    print(f"    属性数量: {len(attributes)}")
                    
                    for attr in attributes:
                        print(f"      - {attr.name}: {attr.value}")
                    
                    print()
                
                # 4. 模拟前端数据结构
                print(f"🌐 模拟前端数据结构:")
                attribute_groups_with_config = []
                
                for group in groups:
                    group_attributes = Attribute.query.filter_by(group_id=group.id, is_active=True).order_by(Attribute.sort_order, Attribute.name).all()
                    
                    # 只包含有属性的组，或者启用了自定义输入的组
                    if group_attributes or group.allow_custom_input:
                        config = {
                            'group': group,
                            'attributes': group_attributes,
                            'allow_custom_input': group.allow_custom_input,
                            'custom_input_type': group.custom_input_type,
                            'custom_input_label': group.custom_input_label,
                            'custom_input_placeholder': group.custom_input_placeholder
                        }
                        attribute_groups_with_config.append(config)
                        
                        print(f"  属性组: {group.name}")
                        print(f"    显示方式: {group.display_type}")
                        print(f"    属性数量: {len(group_attributes)}")
                        print(f"    允许自定义: {group.allow_custom_input}")
                        
                        if group.allow_custom_input:
                            print(f"    自定义标签: '{group.custom_input_label}'")
                            print(f"    输入类型: {group.custom_input_type}")
                            print(f"    占位符: '{group.custom_input_placeholder}'")
                        
                        # 检查模板条件
                        if group.display_type == 'select':
                            print(f"    ✅ 应该显示下拉框")
                            if group.allow_custom_input:
                                print(f"    ✅ 应该包含自定义选项")
                            else:
                                print(f"    ❌ 不包含自定义选项")
                        else:
                            print(f"    ⚪ 显示为单选按钮")
                        
                        print()
                
                print(f"📊 前端将显示 {len(attribute_groups_with_config)} 个属性组")
                
                # 5. 检查产品绑定的属性
                print(f"🔗 产品绑定的属性:")
                product_attrs = product.get_attributes_by_group()
                for group_name, attrs in product_attrs.items():
                    print(f"  - {group_name}: {len(attrs)} 个属性")
                    for attr in attrs:
                        print(f"    - {attr.name}: {attr.value}")
            else:
                print(f"❌ 分类不存在")
        else:
            print(f"❌ 产品未绑定分类")

if __name__ == "__main__":
    debug_product_12()
    print("\n📝 调试完成")

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化版城市联动测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            background-color: white;
            color: #333;
            transition: border-color 0.3s ease;
        }
        
        select:focus {
            outline: none;
            border-color: #3498db;
        }
        
        select:disabled {
            background-color: #f8f9fa !important;
            color: #6c757d !important;
            cursor: not-allowed;
            opacity: 0.6;
        }

        /* 确保选择框始终有白色背景 */
        select {
            background-color: white !important;
            color: #333 !important;
        }

        select:disabled {
            background-color: #f8f9fa !important;
            color: #6c757d !important;
        }

        /* 修复选择框选项的背景色 */
        select option {
            background-color: white !important;
            color: #333 !important;
        }

        /* 输入框样式 */
        input[type="text"] {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background-color: white !important;
            color: #333 !important;
        }

        input[type="text"]:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        input[type="text"]:disabled {
            background-color: #f8f9fa !important;
            color: #6c757d !important;
            cursor: not-allowed;
        }
        
        .region-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .result-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        
        .result-section h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        
        .result-content {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .console-log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            margin-right: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.3);
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        @media (max-width: 768px) {
            .region-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 简化版城市联动测试</h1>
        
        <div id="status" class="status success">
            系统就绪，等待初始化...
        </div>
        
        <form>
            <div class="region-row">
                <div class="form-group">
                    <label for="province">省份 *</label>
                    <select id="province">
                        <option value="">请选择省份</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="city">城市 *</label>
                    <select id="city">
                        <option value="">请选择城市</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="district">区县</label>
                    <div id="district-container">
                        <select id="district" style="display: none;">
                            <option value="">请选择区县</option>
                        </select>
                        <input type="text" id="district-input" placeholder="请输入区县名称" style="display: none;">
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin-bottom: 30px;">
                <button type="button" class="btn" onclick="getSelectedRegion()">获取选中区域</button>
                <button type="button" class="btn" onclick="clearConsole()">清空日志</button>
            </div>
        </form>
        
        <div class="result-section">
            <h3>📍 当前选中区域</h3>
            <div id="selectedRegion" class="result-content">等待选择...</div>
        </div>
        
        <div class="result-section">
            <h3>📝 控制台日志</h3>
            <div id="consoleLog" class="console-log">等待日志输出...</div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/simple-region-selector.js') }}"></script>
    <script>
        let regionSelector;
        const statusDiv = document.getElementById('status');
        const selectedRegionDiv = document.getElementById('selectedRegion');
        const consoleLogDiv = document.getElementById('consoleLog');
        
        // 重写console.log和console.error来显示在页面上
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        
        function addToConsole(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] [${type}] ${message}\n`;
            consoleLogDiv.textContent += logEntry;
            consoleLogDiv.scrollTop = consoleLogDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            addToConsole('LOG', message);
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            addToConsole('ERROR', message);
        };
        
        function updateStatus(message, isError = false) {
            statusDiv.textContent = message;
            statusDiv.className = `status ${isError ? 'error' : 'success'}`;
        }
        
        function clearConsole() {
            consoleLogDiv.textContent = '日志已清空...\n';
        }
        
        function getSelectedRegion() {
            if (!regionSelector) {
                updateStatus('区域选择器未初始化', true);
                return;
            }
            
            const region = regionSelector.getSelectedRegion();
            selectedRegionDiv.textContent = JSON.stringify(region, null, 2);
            console.log('手动获取选中区域:', region);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化区域选择器...');
            updateStatus('正在初始化区域选择器...');
            
            try {
                regionSelector = new SimpleRegionSelector({
                    provinceSelector: '#province',
                    citySelector: '#city',
                    districtSelector: '#district',
                    provincePlaceholder: '请选择省份',
                    cityPlaceholder: '请选择城市',
                    districtPlaceholder: '请选择区县',
                    onChange: function(region) {
                        console.log('区域选择变化:', region);
                        selectedRegionDiv.textContent = JSON.stringify(region, null, 2);
                        
                        // 更新状态
                        if (region.province.id) {
                            let statusText = `已选择: ${region.province.name}`;
                            if (region.city.id) {
                                statusText += ` > ${region.city.name}`;
                            }
                            if (region.district.name) {
                                statusText += ` > ${region.district.name}`;
                                if (region.districtMode === 'input') {
                                    statusText += ' (手动输入)';
                                }
                            }
                            updateStatus(statusText);
                        } else {
                            updateStatus('请选择省份');
                        }
                    }
                });
                
                updateStatus('区域选择器初始化成功！');
                console.log('区域选择器初始化完成');
                
            } catch (error) {
                console.error('初始化失败:', error);
                updateStatus('初始化失败: ' + error.message, true);
            }
        });
    </script>
</body>
</html>

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建数据库表
"""

from app import create_app
from models import db

def create_tables():
    """创建所有数据库表"""
    app = create_app()
    with app.app_context():
        try:
            print("开始创建数据库表...")
            
            # 创建所有表
            db.create_all()
            
            print("✅ 数据库表创建成功！")
            
            # 验证表是否创建成功
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            
            print(f"\n创建的表 ({len(tables)} 个):")
            for table in sorted(tables):
                print(f"  - {table}")
                
            # 检查attribute_groups表结构
            if 'attribute_groups' in tables:
                columns = inspector.get_columns('attribute_groups')
                print(f"\nattribute_groups表结构 ({len(columns)} 列):")
                for col in columns:
                    print(f"  - {col['name']}: {col['type']}")
            
            return True
            
        except Exception as e:
            print(f"❌ 创建表失败: {e}")
            return False

if __name__ == "__main__":
    success = create_tables()
    if success:
        print("\n🎉 数据库初始化完成！")
    else:
        print("\n💥 数据库初始化失败！")

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品属性系统测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/product-attributes.css') }}" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .product-header {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .product-image {
            width: 200px;
            height: 200px;
            background: #f0f0f0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }
        .product-info h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .product-info .price {
            font-size: 24px;
            color: #e74c3c;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .attributes-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .price-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 20px;
        }
        .quantity-selector {
            margin-bottom: 20px;
        }
        .quantity-selector .input-group {
            max-width: 200px;
        }
        .btn-add-cart {
            width: 100%;
            padding: 15px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 8px;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 产品头部信息 -->
        <div class="product-header">
            <div class="row">
                <div class="col-md-3">
                    <div class="product-image">
                        产品图片
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="product-info">
                        <h1>名片印刷</h1>
                        <div class="price">¥<span id="base-price">50.00</span></div>
                        <p class="text-muted">高品质名片印刷，支持多种规格和工艺选择</p>
                        
                        <!-- 数量选择 -->
                        <div class="quantity-selector">
                            <label class="form-label">数量</label>
                            <div class="input-group">
                                <button class="btn btn-outline-secondary" type="button" onclick="changeQuantity(-1)">-</button>
                                <input type="number" class="form-control text-center" id="quantity" value="1" min="1" max="9999">
                                <button class="btn btn-outline-secondary" type="button" onclick="changeQuantity(1)">+</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 属性选择区域 -->
            <div class="col-lg-8">
                <div class="attributes-section">
                    <h3 class="mb-4">选择规格</h3>
                    <div id="attribute-container">
                        <!-- 属性选择器将在这里渲染 -->
                        <div class="attribute-loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div>正在加载属性选项...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 价格和购买区域 -->
            <div class="col-lg-4">
                <div class="price-section">
                    <h4 class="mb-3">价格详情</h4>
                    <div id="price-display">
                        <div class="price-breakdown">
                            <div class="base-price">基础价格: ¥50.00</div>
                            <div class="total-price h5">总价: ¥50.00</div>
                        </div>
                    </div>
                    
                    <button class="btn btn-primary btn-add-cart mt-3" onclick="addToCart()">
                        立即下单
                    </button>
                    
                    <!-- 调试信息 -->
                    <div class="debug-info">
                        <strong>调试信息:</strong>
                        <div id="debug-output">等待选择属性...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/product-attributes.js') }}"></script>
    <script>
        let attributeSelector;
        let currentProductId = 1; // 测试用的产品ID

        // 初始化属性选择器
        document.addEventListener('DOMContentLoaded', function() {
            attributeSelector = new ProductAttributeSelector({
                productId: currentProductId,
                container: '#attribute-container',
                priceContainer: '#price-display',
                onPriceChange: function(priceData) {
                    console.log('价格变化:', priceData);
                    updateDebugInfo(priceData);
                },
                onAttributeChange: function(attributeData) {
                    console.log('属性变化:', attributeData);
                    updateDebugInfo(null, attributeData);
                }
            });
        });

        // 数量变化
        function changeQuantity(delta) {
            const quantityInput = document.getElementById('quantity');
            let newQuantity = parseInt(quantityInput.value) + delta;
            newQuantity = Math.max(1, Math.min(9999, newQuantity));
            quantityInput.value = newQuantity;
            
            // 重新计算价格
            if (attributeSelector) {
                attributeSelector.calculatePrice();
            }
        }

        // 监听数量输入变化
        document.getElementById('quantity').addEventListener('change', function() {
            if (attributeSelector) {
                attributeSelector.calculatePrice();
            }
        });

        // 添加到购物车
        function addToCart() {
            if (!attributeSelector) {
                alert('属性选择器未初始化');
                return;
            }

            const selectedData = attributeSelector.getSelectedData();
            const quantity = parseInt(document.getElementById('quantity').value);

            const orderData = {
                productId: currentProductId,
                quantity: quantity,
                selectedAttributes: selectedData.selectedAttributes,
                customAttributes: selectedData.customAttributes,
                attributeQuantities: selectedData.attributeQuantities,
                totalPrice: selectedData.totalPrice
            };

            console.log('下单数据:', orderData);
            
            // 这里可以发送到后端创建订单
            alert(`下单成功！\n总价: ¥${selectedData.totalPrice.toFixed(2)}\n数量: ${quantity}`);
        }

        // 更新调试信息
        function updateDebugInfo(priceData, attributeData) {
            const debugOutput = document.getElementById('debug-output');
            let info = '';
            
            if (attributeData) {
                info += `选中属性: ${JSON.stringify(attributeData.selectedAttributes)}\n`;
                info += `自定义属性: ${JSON.stringify(attributeData.customAttributes)}\n`;
                info += `属性数量: ${JSON.stringify(attributeData.attributeQuantities)}\n`;
            }
            
            if (priceData) {
                info += `\n价格详情:\n`;
                info += `基础价格: ¥${priceData.base_price.toFixed(2)}\n`;
                info += `属性加价: ¥${priceData.total_attribute_price.toFixed(2)}\n`;
                info += `单价: ¥${priceData.unit_price.toFixed(2)}\n`;
                info += `总价: ¥${priceData.total_price.toFixed(2)}\n`;
                
                if (priceData.attribute_details && priceData.attribute_details.length > 0) {
                    info += `\n属性计算详情:\n`;
                    priceData.attribute_details.forEach(detail => {
                        info += `- ${detail.name}: ${detail.value} (+¥${detail.calculated_price.toFixed(2)})\n`;
                    });
                }
            }
            
            debugOutput.textContent = info || '等待选择属性...';
        }

        // 测试数据创建函数
        async function createTestData() {
            try {
                // 这里可以调用API创建测试数据
                console.log('创建测试数据...');
                
                // 示例：创建属性组和属性
                const testData = {
                    groups: [
                        {
                            name: '尺寸',
                            attributes: [
                                { name: '标准名片', value: '90x54mm', price_modifier: 0 },
                                { name: '欧式名片', value: '85x54mm', price_modifier: 5.0 },
                                { name: '自定义尺寸', input_type: 'custom', is_custom_input: true }
                            ]
                        },
                        {
                            name: '材质',
                            attributes: [
                                { name: '300g铜版纸', value: '300g_coated', price_modifier: 0 },
                                { name: '350g艺术纸', value: '350g_art', price_modifier: 8.0 }
                            ]
                        },
                        {
                            name: '数量',
                            attributes: [
                                { name: '100张', value: '100', price_modifier: 0 },
                                { name: '500张', value: '500', price_modifier: 20.0 },
                                { name: '1000张', value: '1000', price_modifier: 35.0 },
                                { name: '自定义数量', input_type: 'number_input', is_custom_input: true, price_formula: 'value * 0.05' }
                            ]
                        }
                    ]
                };
                
                console.log('测试数据:', testData);
                
            } catch (error) {
                console.error('创建测试数据失败:', error);
            }
        }

        // 页面加载完成后可以创建测试数据
        // createTestData();
    </script>
</body>
</html>

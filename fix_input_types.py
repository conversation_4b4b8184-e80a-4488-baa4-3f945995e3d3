#!/usr/bin/env python
# -*- coding: utf-8 -*-

from app import create_app
from models import db
from sqlalchemy import text

app = create_app()

with app.app_context():
    try:
        # 检查现有数据
        result = db.session.execute(text("SELECT DISTINCT input_type FROM attributes"))
        types = [row[0] for row in result.fetchall()]
        print('现有的input_type值:', types)
        
        # 修复有问题的数据
        db.session.execute(text("UPDATE attributes SET input_type = 'textarea' WHERE input_type = 'input'"))
        db.session.commit()
        print('✅ 修复了input类型为textarea')
        
        # 现在更新枚举类型
        print('更新枚举类型...')
        db.session.execute(text("""
            ALTER TABLE attributes MODIFY COLUMN input_type ENUM(
                'select', 'radio', 'checkbox', 'text_input', 'number_input', 
                'textarea', 'file_upload', 'quantity_select', 'date_input', 'color_picker'
            ) DEFAULT 'select'
        """))
        db.session.commit()
        print('✅ 枚举类型更新成功')
        
        # 验证结果
        result = db.session.execute(text("SHOW COLUMNS FROM attributes WHERE Field = 'input_type'"))
        column_info = result.fetchone()
        if column_info:
            print(f'新的枚举类型: {column_info[1]}')
        
    except Exception as e:
        print(f'错误: {str(e)}')
        db.session.rollback()

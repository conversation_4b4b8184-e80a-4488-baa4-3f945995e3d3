#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
最终测试自定义输入功能
"""

from app import create_app
from models import db
from models.product import AttributeGroup, Attribute, Category, Product

def test_custom_input_final():
    """最终测试自定义输入功能"""
    
    app = create_app()
    
    with app.app_context():
        print("🎯 最终测试自定义输入功能...")
        
        # 测试产品12
        product = Product.query.get(12)
        if not product:
            print("❌ 产品12不存在")
            return False
        
        print(f"\n🛍️ 测试产品: {product.name}")
        
        # 获取分类
        category = Category.query.get(product.category_id)
        if not category:
            print("❌ 产品分类不存在")
            return False
        
        print(f"📂 分类: {category.name}")
        
        # 获取属性组配置
        groups = category.attribute_groups.filter_by(is_active=True).order_by(AttributeGroup.sort_order, AttributeGroup.name).all()
        
        print(f"\n🎛️ 属性组配置:")
        custom_enabled_groups = []
        
        for group in groups:
            attributes = Attribute.query.filter_by(group_id=group.id, is_active=True).all()
            
            if attributes or group.allow_custom_input:
                print(f"  - {group.name}:")
                print(f"    显示方式: {group.display_type}")
                print(f"    属性数量: {len(attributes)}")
                print(f"    允许自定义: {group.allow_custom_input}")
                
                if group.allow_custom_input:
                    custom_enabled_groups.append(group)
                    print(f"    ✅ 自定义配置:")
                    print(f"      类型: {group.custom_input_type}")
                    print(f"      标签: '{group.custom_input_label}'")
                    print(f"      占位符: '{group.custom_input_placeholder}'")
                    
                    if group.display_type == 'select':
                        print(f"      📋 下拉框模式 - 应显示自定义选项")
                    else:
                        print(f"      🔘 单选按钮模式 - 应显示自定义单选按钮")
                else:
                    print(f"    ❌ 无自定义输入")
                print()
        
        # 生成测试报告
        print(f"📊 测试结果:")
        print(f"  - 总属性组数: {len(groups)}")
        print(f"  - 启用自定义输入的属性组: {len(custom_enabled_groups)}")
        
        if custom_enabled_groups:
            print(f"\n✅ 自定义输入功能已启用！")
            print(f"🌐 测试页面: http://127.0.0.1:5000/product/{product.id}")
            
            print(f"\n📝 预期显示效果:")
            for group in custom_enabled_groups:
                if group.display_type == 'select':
                    print(f"  - {group.name}: 下拉框最后一项应为 '{group.custom_input_label}'")
                    print(f"    选择后应显示 {group.custom_input_type} 输入框")
                else:
                    print(f"  - {group.name}: 单选按钮最后一项应为 '{group.custom_input_label}'")
                    print(f"    选择后应显示 {group.custom_input_type} 输入框")
            
            return True
        else:
            print(f"\n❌ 该产品无自定义输入功能")
            return False

def suggest_test_steps():
    """建议测试步骤"""
    print(f"\n🧪 建议测试步骤:")
    print(f"1. 打开浏览器访问: http://127.0.0.1:5000/product/12")
    print(f"2. 查看商品规格区域")
    print(f"3. 对于启用自定义输入的属性组:")
    print(f"   - 下拉框模式: 查看是否有'自定义'选项")
    print(f"   - 单选按钮模式: 查看是否有'自定义'单选按钮")
    print(f"4. 选择自定义选项，验证:")
    print(f"   - 是否显示对应的输入框")
    print(f"   - 输入框类型是否正确（文本/数字/长文本）")
    print(f"   - 占位符文本是否正确")
    print(f"5. 输入自定义值，验证:")
    print(f"   - 实时验证是否工作")
    print(f"   - 价格计算是否正确")
    print(f"6. 清除自定义输入，验证:")
    print(f"   - 点击清除按钮是否正常工作")
    print(f"   - 输入框是否正确隐藏")

if __name__ == "__main__":
    print("🚀 开始最终自定义输入功能测试...")
    
    success = test_custom_input_final()
    
    if success:
        print("\n🎉 自定义输入功能测试通过！")
        suggest_test_steps()
    else:
        print("\n❌ 自定义输入功能测试失败")
    
    print("\n📝 测试完成")

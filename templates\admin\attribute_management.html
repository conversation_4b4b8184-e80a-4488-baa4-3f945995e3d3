<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>属性管理 - 管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .attribute-group-card {
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .attribute-item {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
            cursor: move;
        }
        .attribute-item:hover {
            background-color: #e9ecef;
        }
        .price-badge {
            font-size: 0.8em;
        }
        .price-badge.positive {
            background-color: #28a745;
        }
        .price-badge.negative {
            background-color: #dc3545;
        }
        .price-badge.neutral {
            background-color: #6c757d;
        }
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        .sortable-ghost {
            opacity: 0.4;
        }
        .sortable-chosen {
            background-color: #e7f3ff;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-2 d-none d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>商品管理</span>
                    </h6>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/admin">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/products">
                                <i class="fas fa-box me-2"></i>
                                商品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/admin/attributes">
                                <i class="fas fa-tags me-2"></i>
                                属性管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/orders">
                                <i class="fas fa-shopping-cart me-2"></i>
                                订单管理
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-10 ms-sm-auto main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">属性管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-primary" onclick="showCreateGroupModal()">
                                <i class="fas fa-plus me-1"></i>
                                新建属性组
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="showImportModal()">
                                <i class="fas fa-upload me-1"></i>
                                批量导入
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 属性组列表 -->
                <div class="row">
                    <div class="col-12">
                        <div id="attribute-groups-container">
                            <!-- 属性组将在这里动态加载 -->
                            <div class="text-center py-5">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2 text-muted">正在加载属性组...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 创建属性组模态框 -->
    <div class="modal fade" id="createGroupModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建属性组</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createGroupForm">
                        <div class="mb-3">
                            <label for="groupName" class="form-label">属性组名称</label>
                            <input type="text" class="form-control" id="groupName" required>
                        </div>
                        <div class="mb-3">
                            <label for="groupDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="groupDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="displayType" class="form-label">显示类型</label>
                            <select class="form-select" id="displayType">
                                <option value="radio">单选按钮</option>
                                <option value="select">下拉选择</option>
                                <option value="checkbox">多选框</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createAttributeGroup()">创建</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建属性模态框 -->
    <div class="modal fade" id="createAttributeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建属性</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createAttributeForm">
                        <input type="hidden" id="attributeGroupId">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="attributeName" class="form-label">属性名称</label>
                                    <input type="text" class="form-control" id="attributeName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="attributeValue" class="form-label">属性值</label>
                                    <input type="text" class="form-control" id="attributeValue" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="inputType" class="form-label">输入类型</label>
                                    <select class="form-select" id="inputType" onchange="toggleInputTypeOptions()">
                                        <option value="radio">单选按钮</option>
                                        <option value="select">下拉选择</option>
                                        <option value="checkbox">多选框</option>
                                        <option value="text_input">文本输入</option>
                                        <option value="number_input">数字输入</option>
                                        <option value="textarea">长文本</option>
                                        <option value="file_upload">文件上传</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="attributeCategory" class="form-label">属性类别</label>
                                    <select class="form-select" id="attributeCategory">
                                        <option value="standard">标准属性</option>
                                        <option value="custom">自定义属性</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="priceModifier" class="form-label">价格调整</label>
                                    <input type="number" class="form-control" id="priceModifier" step="0.01" value="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="priceModifierType" class="form-label">调整类型</label>
                                    <select class="form-select" id="priceModifierType">
                                        <option value="fixed">固定金额</option>
                                        <option value="percentage">百分比</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 自定义输入选项 -->
                        <div id="customInputOptions" style="display: none;">
                            <div class="mb-3">
                                <label for="inputPlaceholder" class="form-label">输入提示</label>
                                <input type="text" class="form-control" id="inputPlaceholder">
                            </div>
                            <div class="mb-3">
                                <label for="validationPattern" class="form-label">验证规则（正则表达式）</label>
                                <input type="text" class="form-control" id="validationPattern">
                            </div>
                            <div class="mb-3">
                                <label for="validationMessage" class="form-label">验证失败提示</label>
                                <input type="text" class="form-control" id="validationMessage">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createAttribute()">创建</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script>
        let attributeGroups = [];
        let currentGroupId = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadAttributeGroups();
        });

        // 加载属性组
        async function loadAttributeGroups() {
            try {
                // 这里应该调用API获取属性组数据
                // 暂时使用模拟数据
                const mockData = [
                    {
                        id: 1,
                        name: '尺寸',
                        description: '名片尺寸规格',
                        display_type: 'radio',
                        attributes: [
                            { id: 54, name: '标准名片', value: '90×54mm', price_modifier: 0 },
                            { id: 55, name: '欧式名片', value: '85×54mm', price_modifier: 5 },
                            { id: 56, name: '方形名片', value: '54×54mm', price_modifier: 8 }
                        ]
                    },
                    {
                        id: 2,
                        name: '材质',
                        description: '名片纸张材质',
                        display_type: 'select',
                        attributes: [
                            { id: 57, name: '300g铜版纸', value: '300g铜版纸', price_modifier: 0 },
                            { id: 58, name: '350g艺术纸', value: '350g艺术纸', price_modifier: 10 },
                            { id: 59, name: '400g特种纸', value: '400g特种纸', price_modifier: 20 }
                        ]
                    }
                ];

                attributeGroups = mockData;
                renderAttributeGroups();
            } catch (error) {
                console.error('加载属性组失败:', error);
                showError('加载属性组失败');
            }
        }

        // 渲染属性组
        function renderAttributeGroups() {
            const container = document.getElementById('attribute-groups-container');
            
            if (attributeGroups.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无属性组</h5>
                        <p class="text-muted">点击"新建属性组"开始创建</p>
                    </div>
                `;
                return;
            }

            let html = '';
            attributeGroups.forEach(group => {
                html += `
                    <div class="card attribute-group-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-0">${group.name}</h5>
                                <small class="text-muted">${group.description || ''}</small>
                            </div>
                            <div>
                                <span class="badge bg-info me-2">${group.display_type}</span>
                                <button class="btn btn-sm btn-outline-primary me-1" onclick="showCreateAttributeModal(${group.id})">
                                    <i class="fas fa-plus"></i> 添加属性
                                </button>
                                <button class="btn btn-sm btn-outline-secondary me-1" onclick="editGroup(${group.id})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteGroup(${group.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="attributes-${group.id}" class="attributes-container">
                                ${renderAttributes(group.attributes)}
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;

            // 初始化拖拽排序
            attributeGroups.forEach(group => {
                const container = document.getElementById(`attributes-${group.id}`);
                if (container) {
                    new Sortable(container, {
                        animation: 150,
                        ghostClass: 'sortable-ghost',
                        chosenClass: 'sortable-chosen',
                        onEnd: function(evt) {
                            // 这里可以调用API更新排序
                            console.log('属性排序更新:', evt);
                        }
                    });
                }
            });
        }

        // 渲染属性列表
        function renderAttributes(attributes) {
            if (!attributes || attributes.length === 0) {
                return '<p class="text-muted">暂无属性</p>';
            }

            return attributes.map(attr => {
                const priceClass = attr.price_modifier > 0 ? 'positive' : 
                                 attr.price_modifier < 0 ? 'negative' : 'neutral';
                const priceText = attr.price_modifier > 0 ? `+¥${attr.price_modifier}` :
                                attr.price_modifier < 0 ? `¥${attr.price_modifier}` : '¥0';

                return `
                    <div class="attribute-item d-flex justify-content-between align-items-center" data-id="${attr.id}">
                        <div>
                            <strong>${attr.name}</strong>
                            <span class="text-muted ms-2">${attr.value}</span>
                            <span class="badge price-badge ${priceClass} ms-2">${priceText}</span>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-secondary me-1" onclick="editAttribute(${attr.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteAttribute(${attr.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 显示创建属性组模态框
        function showCreateGroupModal() {
            const modal = new bootstrap.Modal(document.getElementById('createGroupModal'));
            modal.show();
        }

        // 显示创建属性模态框
        function showCreateAttributeModal(groupId) {
            currentGroupId = groupId;
            document.getElementById('attributeGroupId').value = groupId;
            const modal = new bootstrap.Modal(document.getElementById('createAttributeModal'));
            modal.show();
        }

        // 切换输入类型选项
        function toggleInputTypeOptions() {
            const inputType = document.getElementById('inputType').value;
            const customOptions = document.getElementById('customInputOptions');
            
            if (['text_input', 'number_input', 'textarea', 'file_upload'].includes(inputType)) {
                customOptions.style.display = 'block';
                document.getElementById('attributeCategory').value = 'custom';
            } else {
                customOptions.style.display = 'none';
                document.getElementById('attributeCategory').value = 'standard';
            }
        }

        // 创建属性组
        function createAttributeGroup() {
            const name = document.getElementById('groupName').value;
            const description = document.getElementById('groupDescription').value;
            const displayType = document.getElementById('displayType').value;

            if (!name) {
                alert('请输入属性组名称');
                return;
            }

            // 这里应该调用API创建属性组
            console.log('创建属性组:', { name, description, displayType });
            
            // 模拟创建成功
            alert('属性组创建成功！');
            bootstrap.Modal.getInstance(document.getElementById('createGroupModal')).hide();
            loadAttributeGroups();
        }

        // 创建属性
        function createAttribute() {
            const groupId = document.getElementById('attributeGroupId').value;
            const name = document.getElementById('attributeName').value;
            const value = document.getElementById('attributeValue').value;
            const inputType = document.getElementById('inputType').value;
            const category = document.getElementById('attributeCategory').value;
            const priceModifier = document.getElementById('priceModifier').value;
            const priceModifierType = document.getElementById('priceModifierType').value;

            if (!name || !value) {
                alert('请填写属性名称和值');
                return;
            }

            // 这里应该调用API创建属性
            console.log('创建属性:', {
                groupId, name, value, inputType, category, priceModifier, priceModifierType
            });
            
            // 模拟创建成功
            alert('属性创建成功！');
            bootstrap.Modal.getInstance(document.getElementById('createAttributeModal')).hide();
            loadAttributeGroups();
        }

        // 编辑属性组
        function editGroup(groupId) {
            console.log('编辑属性组:', groupId);
            // 实现编辑功能
        }

        // 删除属性组
        function deleteGroup(groupId) {
            if (confirm('确定要删除这个属性组吗？这将同时删除组内所有属性。')) {
                console.log('删除属性组:', groupId);
                // 实现删除功能
            }
        }

        // 编辑属性
        function editAttribute(attributeId) {
            console.log('编辑属性:', attributeId);
            // 实现编辑功能
        }

        // 删除属性
        function deleteAttribute(attributeId) {
            if (confirm('确定要删除这个属性吗？')) {
                console.log('删除属性:', attributeId);
                // 实现删除功能
            }
        }

        // 显示错误信息
        function showError(message) {
            const container = document.getElementById('attribute-groups-container');
            container.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                </div>
            `;
        }
    </script>
</body>
</html>

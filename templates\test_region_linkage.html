<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>区域联动测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        select, input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        select:disabled, input:disabled {
            background-color: #f9f9f9;
            color: #999;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .result h3 {
            margin-top: 0;
            color: #007bff;
        }
        .result pre {
            background: #fff;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
        .district-mode {
            margin-top: 10px;
            padding: 10px;
            background-color: #e7f3ff;
            border-radius: 4px;
            font-size: 14px;
            color: #0066cc;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>区域联动测试</h1>
        
        <form id="regionForm">
            <div class="form-group">
                <label for="province">省份 *</label>
                <select id="province" name="province" required>
                    <option value="">请选择省份</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="city">城市 *</label>
                <select id="city" name="city" required disabled>
                    <option value="">请选择城市</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="district">区县</label>
                <select id="district" name="district" disabled>
                    <option value="">请选择区县</option>
                </select>
                <input type="text" id="districtInput" name="districtInput" placeholder="请输入区县名称" class="hidden" disabled>
                <div id="districtMode" class="district-mode hidden"></div>
            </div>
        </form>
        
        <div class="result">
            <h3>选择结果</h3>
            <pre id="resultDisplay">请选择省市区县...</pre>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/simple-region-selector.js') }}"></script>
    <script>
        let regionSelector = null;

        // 初始化地址选择器
        function initRegionSelector() {
            try {
                regionSelector = new SimpleRegionSelector({
                    provinceSelector: '#province',
                    citySelector: '#city',
                    districtSelector: '#district',
                    districtInputSelector: '#districtInput',
                    provincePlaceholder: '请选择省份',
                    cityPlaceholder: '请选择城市',
                    districtPlaceholder: '请选择区县',
                    onChange: function(region) {
                        console.log('地址选择变化:', region);
                        updateResult(region);
                        updateDistrictModeDisplay();
                    }
                });
                console.log('地址选择器初始化成功');
            } catch (error) {
                console.error('地址选择器初始化失败:', error);
            }
        }

        // 更新结果显示
        function updateResult(region) {
            const resultDisplay = document.getElementById('resultDisplay');
            const result = {
                province: region.province,
                city: region.city,
                district: region.district,
                districtMode: region.districtMode,
                selectedData: regionSelector ? regionSelector.getSelectedRegion() : null
            };
            resultDisplay.textContent = JSON.stringify(result, null, 2);
        }

        // 更新区县模式显示
        function updateDistrictModeDisplay() {
            const districtModeDiv = document.getElementById('districtMode');
            if (regionSelector) {
                const mode = regionSelector.districtMode;
                if (mode) {
                    districtModeDiv.textContent = `当前区县模式: ${mode === 'select' ? '选择框模式（有区县数据）' : '输入框模式（无区县数据）'}`;
                    districtModeDiv.classList.remove('hidden');
                } else {
                    districtModeDiv.classList.add('hidden');
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initRegionSelector();
        });
    </script>
</body>
</html>

#!/usr/bin/env python
# -*- coding: utf-8 -*-

from app import create_app
from models import db
from sqlalchemy import text

app = create_app()

with app.app_context():
    try:
        # 检查新字段是否存在
        result = db.session.execute(text("SHOW COLUMNS FROM attributes LIKE 'attribute_category'"))
        has_category = result.fetchone() is not None
        print(f"attribute_category字段: {'✅ 存在' if has_category else '❌ 不存在'}")
        
        result = db.session.execute(text("SHOW COLUMNS FROM attributes LIKE 'validation_rules'"))
        has_validation = result.fetchone() is not None
        print(f"validation_rules字段: {'✅ 存在' if has_validation else '❌ 不存在'}")
        
        result = db.session.execute(text("SHOW COLUMNS FROM attributes LIKE 'price_calculation_rules'"))
        has_price_rules = result.fetchone() is not None
        print(f"price_calculation_rules字段: {'✅ 存在' if has_price_rules else '❌ 不存在'}")
        
        # 检查新表是否存在
        result = db.session.execute(text("SHOW TABLES LIKE 'attribute_values'"))
        has_values_table = result.fetchone() is not None
        print(f"attribute_values表: {'✅ 存在' if has_values_table else '❌ 不存在'}")
        
        result = db.session.execute(text("SHOW TABLES LIKE 'attribute_templates'"))
        has_templates_table = result.fetchone() is not None
        print(f"attribute_templates表: {'✅ 存在' if has_templates_table else '❌ 不存在'}")
        
        # 检查枚举类型
        result = db.session.execute(text("SHOW COLUMNS FROM attributes WHERE Field = 'input_type'"))
        column_info = result.fetchone()
        if column_info:
            column_type = column_info[1]  # Type字段
            has_new_types = 'text_input' in column_type and 'number_input' in column_type
            print(f"新的input_type枚举: {'✅ 已更新' if has_new_types else '❌ 未更新'}")
            print(f"  当前类型: {column_type}")
        
        print("\n迁移检查完成！")
        
    except Exception as e:
        print(f"检查过程中出现错误: {str(e)}")

<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#667eea">
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='images/favicon.svg') }}">

    <title>{% block title %}管理后台 - {{ site_config.site_name }}{% endblock %}</title>

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Modern CSS Framework -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Modern Design System -->
    <link href="{{ url_for('static', filename='css/modern-design-system.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/modern-components.css') }}" rel="stylesheet">
    <!-- 管理后台现代化样式 -->
    <link href="{{ url_for('static', filename='css/admin-modern.css') }}" rel="stylesheet">

    {% block extra_css %}{% endblock %}
</head>
<body class="admin-body">
    <!-- 现代化顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-glass admin-navbar sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand admin-brand" href="{{ url_for('admin.index') }}">
                <i class="fas fa-cogs me-2"></i>管理后台
            </a>

            <!-- 主题切换按钮 -->
            <div class="navbar-nav me-auto">
                <button class="btn btn-link nav-link border-0 me-3" onclick="toggleTheme()" title="切换主题">
                    <i class="fas fa-moon" id="theme-icon"></i>
                </button>
            </div>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle admin-user-menu" href="#" role="button" data-bs-toggle="dropdown">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <span class="ms-2">{{ current_user.username }}</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-modern">
                        <li><h6 class="dropdown-header">
                            <i class="fas fa-crown me-2 text-warning"></i>管理员
                        </h6></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('main.index') }}">
                            <i class="fas fa-home me-2"></i>返回首页
                        </a></li>
                        <li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('auth.profile') }}">
                            <i class="fas fa-user-circle me-2"></i>个人资料
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item dropdown-item-modern text-danger" href="{{ url_for('auth.logout') }}">
                            <i class="fas fa-sign-out-alt me-2"></i>退出登录
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>
    
    <div class="admin-container">
        <div class="row g-0">
            <!-- 现代化侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block admin-sidebar">
                <div class="sidebar-content">
                    <div class="sidebar-menu">
                        <!-- 仪表盘 -->
                        <div class="menu-item">
                            <a class="menu-link {{ 'active' if request.endpoint == 'admin.index' else '' }}"
                               href="{{ url_for('admin.index') }}">
                                <div class="menu-icon">
                                    <i class="fas fa-tachometer-alt"></i>
                                </div>
                                <span class="menu-text">仪表盘</span>
                            </a>
                        </div>

                        <!-- 商品管理 -->
                        <div class="menu-section">
                            <div class="menu-section-title">
                                <i class="fas fa-shopping-bag me-2"></i>商品管理
                            </div>

                            <div class="menu-item">
                                <a class="menu-link {{ 'active' if 'categories' in request.endpoint else '' }}"
                                   href="{{ url_for('admin.categories') }}">
                                    <div class="menu-icon">
                                        <i class="fas fa-tags"></i>
                                    </div>
                                    <span class="menu-text">分类管理</span>
                                </a>
                            </div>

                            <div class="menu-item">
                                <a class="menu-link {{ 'active' if 'attribute' in request.endpoint else '' }}"
                                   href="{{ url_for('admin.attribute_groups') }}">
                                    <div class="menu-icon">
                                        <i class="fas fa-list"></i>
                                    </div>
                                    <span class="menu-text">属性管理</span>
                                </a>
                            </div>

                            <div class="menu-item">
                                <a class="menu-link {{ 'active' if 'products' in request.endpoint else '' }}"
                                   href="{{ url_for('admin.products') }}">
                                    <div class="menu-icon">
                                        <i class="fas fa-box"></i>
                                    </div>
                                    <span class="menu-text">商品管理</span>
                                </a>
                            </div>
                        </div>

                        <!-- 订单管理 -->
                        <div class="menu-section">
                            <div class="menu-section-title">
                                <i class="fas fa-shopping-cart me-2"></i>订单管理
                            </div>

                            <div class="menu-item">
                                <a class="menu-link {{ 'active' if 'orders' in request.endpoint else '' }}"
                                   href="{{ url_for('admin.orders') }}">
                                    <div class="menu-icon">
                                        <i class="fas fa-receipt"></i>
                                    </div>
                                    <span class="menu-text">订单列表</span>
                                </a>
                            </div>
                        </div>

                        <!-- 系统管理 -->
                        <div class="menu-section">
                            <div class="menu-section-title">
                                <i class="fas fa-cogs me-2"></i>系统管理
                            </div>

                            <div class="menu-item">
                                <a class="menu-link {{ 'active' if 'users' in request.endpoint else '' }}"
                                   href="{{ url_for('admin.users') }}">
                                    <div class="menu-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <span class="menu-text">用户管理</span>
                                </a>
                            </div>

                            <div class="menu-item">
                                <a class="menu-link {{ 'active' if 'settings' in request.endpoint else '' }}"
                                   href="{{ url_for('admin.settings') }}">
                                    <div class="menu-icon">
                                        <i class="fas fa-cog"></i>
                                    </div>
                                    <span class="menu-text">系统设置</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- 现代化主要内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 admin-main">
                <div class="admin-content">
                    <!-- 现代化消息提示 -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                        <div class="alerts-container">
                            {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-modern alert-dismissible fade show" role="alert">
                                <div class="alert-content">
                                    <div class="alert-icon">
                                        {% if category == 'success' %}
                                        <i class="fas fa-check-circle"></i>
                                        {% elif category == 'error' or category == 'danger' %}
                                        <i class="fas fa-exclamation-circle"></i>
                                        {% elif category == 'warning' %}
                                        <i class="fas fa-exclamation-triangle"></i>
                                        {% else %}
                                        <i class="fas fa-info-circle"></i>
                                        {% endif %}
                                    </div>
                                    <div class="alert-message">{{ message }}</div>
                                </div>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    {% endwith %}

                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>

    <!-- Modern JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Theme Toggle Script -->
    <script>
        // 主题切换功能
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-bs-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            html.setAttribute('data-bs-theme', newTheme);
            localStorage.setItem('admin-theme', newTheme);

            // 更新图标
            const icon = document.getElementById('theme-icon');
            if (icon) {
                icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }

        // 页面加载时恢复主题
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('admin-theme') || 'light';
            document.documentElement.setAttribute('data-bs-theme', savedTheme);

            const icon = document.getElementById('theme-icon');
            if (icon) {
                icon.className = savedTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        });

        // 页面进入动画
        document.body.classList.add('page-enter');
    </script>

    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/admin.js') }}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全国区域数据统计</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        .province-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .province-table th,
        .province-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .province-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .province-table tr:hover {
            background-color: #f5f5f5;
        }
        .has-districts {
            background-color: #d4edda;
        }
        .no-districts {
            background-color: #f8d7da;
        }
        .legend {
            margin-bottom: 20px;
            display: flex;
            gap: 20px;
            align-items: center;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #007bff;
            text-decoration: none;
            padding: 8px 16px;
            border: 1px solid #007bff;
            border-radius: 4px;
            transition: all 0.3s;
        }
        .back-link:hover {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/test-region-linkage" class="back-link">← 返回联动测试</a>
        
        <h1>全国区域数据统计</h1>
        
        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_provinces }}</div>
                <div class="stat-label">省份/直辖市/自治区</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_cities }}</div>
                <div class="stat-label">城市</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_districts }}</div>
                <div class="stat-label">区县</div>
            </div>
        </div>
        
        <h2>各省份数据详情</h2>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color has-districts"></div>
                <span>有完整区县数据</span>
            </div>
            <div class="legend-item">
                <div class="legend-color no-districts"></div>
                <span>仅有城市数据</span>
            </div>
        </div>
        
        <table class="province-table">
            <thead>
                <tr>
                    <th>省份/直辖市/自治区</th>
                    <th>省份代码</th>
                    <th>城市数量</th>
                    <th>区县数量</th>
                    <th>数据完整度</th>
                </tr>
            </thead>
            <tbody>
                {% for province in stats.province_stats %}
                <tr class="{% if province.district_count > 0 %}has-districts{% else %}no-districts{% endif %}">
                    <td>{{ province.name }}</td>
                    <td>{{ province.id }}</td>
                    <td>{{ province.city_count }}</td>
                    <td>{{ province.district_count }}</td>
                    <td>
                        {% if province.district_count > 0 %}
                            <strong>完整</strong> (省市区县)
                        {% else %}
                            基础 (省市)
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <div style="margin-top: 30px; padding: 20px; background-color: #e9ecef; border-radius: 8px;">
            <h3>数据说明</h3>
            <ul>
                <li><strong>完整数据省份</strong>：广东省、江苏省、浙江省、上海市、湖南省 - 包含详细的区县信息</li>
                <li><strong>基础数据省份</strong>：其他省份 - 包含省份和城市信息，区县数据为空</li>
                <li><strong>联动处理</strong>：
                    <ul>
                        <li>有区县数据的城市：显示选择框供用户选择</li>
                        <li>无区县数据的城市：显示输入框，默认值为空格</li>
                    </ul>
                </li>
                <li><strong>数据来源</strong>：
                    <ul>
                        <li>省市数据：liang1024_citys.json</li>
                        <li>详细区县数据：JS2025.json, ZJ2025.json, SH2025.json, GD2025.json, hunan2025.json</li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</body>
</html>

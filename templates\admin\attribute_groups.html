{% extends "admin/base.html" %}

{% block title %}属性组管理 - 管理后台{% endblock %}

{% block content %}
<!-- 现代化页面头部 -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div class="page-title">
            <h1 class="h2 mb-1">
                <i class="fas fa-layer-group me-3 text-primary"></i>属性组管理
            </h1>
            {% if selected_category %}
            <p class="text-muted mb-0">
                <i class="fas fa-tag me-1"></i>{{ selected_category.name }}
            </p>
            {% else %}
            <p class="text-muted mb-0">管理商品属性的分组结构</p>
            {% endif %}
        </div>
        <div class="page-actions">
            <a href="{{ url_for('admin.add_attribute_group') }}{% if category_id %}?category_id={{ category_id }}{% endif %}"
               class="btn btn-primary btn-modern">
                <i class="fas fa-plus me-2"></i>添加属性组
            </a>
        </div>
    </div>
</div>

<!-- 现代化筛选卡片 -->
<div class="card card-modern mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="filter-section">
                    <div class="filter-group">
                        <label for="categoryFilter" class="filter-label">
                            <i class="fas fa-filter me-2"></i>选择分类
                        </label>
                        <select id="categoryFilter" class="form-select form-select-modern" onchange="handleCategoryChange(this.value)">
                            <option value="">请选择分类</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if category_id == category.id %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <button type="button" id="refreshBtn" class="btn btn-outline-primary btn-modern" onclick="window.location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>刷新
                    </button>
                </div>
            </div>
            <div class="col-md-4 text-end">
                {% if selected_category %}
                <a href="{{ url_for('admin.categories') }}" class="btn btn-outline-secondary btn-modern">
                    <i class="fas fa-arrow-left me-2"></i>返回分类管理
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 现代化内容卡片 -->
<div class="card card-modern">
    <div class="card-body">
        {% if show_empty_state %}
        <!-- 现代化空状态 -->
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="fas fa-layer-group"></i>
            </div>
            <h5 class="empty-state-title">请选择分类查看属性组</h5>
            <p class="empty-state-description">请从上方下拉框中选择一个分类，然后查看该分类下的属性组</p>
            <div class="empty-state-tip">
                <i class="fas fa-lightbulb me-2 text-warning"></i>
                <span>属性组用于组织商品的属性选项，每个分类可以有多个属性组</span>
            </div>
        </div>

        {% elif groups and groups.items %}
        <!-- 现代化数据展示 -->
        <div class="data-header">
            <div class="data-title">
                <h6 class="mb-0">{{ selected_category.name }} - 属性组列表</h6>
            </div>
            <div class="data-stats">
                <span class="badge badge-modern bg-primary">共 {{ groups.total }} 个</span>
            </div>
        </div>

        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-modern">
                    <thead>
                        <tr>
                            <th class="table-id">ID</th>
                            <th class="table-name">属性组名称</th>
                            <th class="table-desc">描述</th>
                            <th class="table-count">属性数量</th>
                            <th class="table-sort">排序</th>
                            <th class="table-status">状态</th>
                            <th class="table-date">创建时间</th>
                            <th class="table-actions">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for group in groups.items %}
                        <tr class="table-row">
                            <td class="table-id">
                                <span class="id-badge">#{{ group.id }}</span>
                            </td>
                            <td class="table-name">
                                <div class="name-cell">
                                    <span class="name-text">{{ group.name }}</span>
                                </div>
                            </td>
                            <td class="table-desc">
                                <span class="desc-text">
                                    {{ group.description[:50] + '...' if group.description and group.description|length > 50 else group.description or '-' }}
                                </span>
                            </td>
                            <td class="table-count">
                                {% set attr_count = group.get_attributes_count() %}
                                <div class="count-cell">
                                    <span class="badge badge-modern bg-info">{{ attr_count }}</span>
                                    {% if attr_count > 0 %}
                                    <a href="{{ url_for('admin.attributes', group_id=group.id, from_group=group.id, locked=True) }}"
                                       class="btn btn-sm btn-outline-info btn-modern ms-2" title="查看属性">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="table-sort">
                                <span class="sort-badge">{{ group.sort_order }}</span>
                            </td>
                            <td class="table-status">
                                <span class="badge badge-modern bg-{{ 'success' if group.is_active else 'secondary' }}">
                                    <i class="fas fa-{{ 'check' if group.is_active else 'times' }} me-1"></i>
                                    {{ '启用' if group.is_active else '禁用' }}
                                </span>
                            </td>
                            <td class="table-date">
                                <span class="date-text">{{ group.created_at.strftime('%Y-%m-%d') }}</span>
                            </td>
                            <td class="table-actions">
                                <div class="action-buttons">
                                    <a href="{{ url_for('admin.add_attribute', group_id=group.id, from_group=group.id, locked=True) }}"
                                       class="btn btn-sm btn-success btn-modern" title="添加属性">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                    <a href="{{ url_for('admin.edit_attribute_group', id=group.id) }}"
                                       class="btn btn-sm btn-outline-primary btn-modern" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger btn-modern delete-btn" title="删除"
                                            data-url="{{ url_for('admin.delete_attribute_group', id=group.id) }}"
                                            data-name="{{ group.name }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 分页 -->
        {% if groups.pages > 1 %}
        <nav aria-label="属性组分页">
            <ul class="pagination justify-content-center">
                {% if groups.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.attribute_groups', page=groups.prev_num, category_id=category_id) }}">上一页</a>
                </li>
                {% endif %}

                {% for page_num in groups.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != groups.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.attribute_groups', page=page_num, category_id=category_id) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if groups.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.attribute_groups', page=groups.next_num, category_id=category_id) }}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <!-- 现代化无数据状态 -->
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="fas fa-layer-group"></i>
            </div>
            <h5 class="empty-state-title">{{ selected_category.name }} 暂无属性组</h5>
            <p class="empty-state-description">还没有为该分类创建任何属性组</p>
            <div class="empty-state-action">
                <a href="{{ url_for('admin.add_attribute_group') }}?category_id={{ category_id }}"
                   class="btn btn-primary btn-modern">
                    <i class="fas fa-plus me-2"></i>为该分类添加第一个属性组
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 现代化删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modal-modern">
            <div class="modal-header modal-header-modern">
                <div class="modal-title-wrapper">
                    <div class="modal-icon">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                    </div>
                    <h5 class="modal-title">确认删除</h5>
                </div>
                <button type="button" class="btn-close btn-close-modern" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body modal-body-modern">
                <div class="delete-confirmation">
                    <p class="confirmation-text">确定要删除属性组 "<span id="deleteName" class="text-primary fw-bold"></span>" 吗？</p>
                    <div class="warning-box">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>删除后无法恢复，请谨慎操作。</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer modal-footer-modern">
                <button type="button" class="btn btn-secondary btn-modern" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>取消
                </button>
                <button type="button" class="btn btn-danger btn-modern" id="confirmDelete">
                    <i class="fas fa-trash me-2"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全局函数：处理分类变化
function handleCategoryChange(categoryId) {
    console.log('🔍 分类选择变化:', categoryId);

    // 构建新的URL
    let newUrl = window.location.pathname;
    if (categoryId && categoryId !== '') {
        newUrl += '?category_id=' + categoryId;
    }

    console.log('🔗 跳转到:', newUrl);

    // 立即跳转
    window.location.href = newUrl;
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 初始化大属性组页面删除功能');
    
    // 初始化删除功能
    initDeleteFunction();
});

function initDeleteFunction() {
    const deleteModal = document.getElementById('deleteModal');
    const deleteNameSpan = document.getElementById('deleteName');
    const confirmDeleteBtn = document.getElementById('confirmDelete');
    let currentDeleteUrl = '';
    let currentDeleteName = '';
    
    if (!deleteModal) {
        console.error('删除模态框未找到');
        return;
    }
    
    const modal = new bootstrap.Modal(deleteModal);
    
    // 移除所有现有的事件监听器
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(btn => {
        // 克隆节点以移除所有事件监听器
        const newBtn = btn.cloneNode(true);
        btn.parentNode.replaceChild(newBtn, btn);
    });
    
    // 重新绑定删除按钮事件
    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            currentDeleteUrl = this.dataset.url;
            currentDeleteName = this.dataset.name;
            
            console.log('点击删除按钮:', currentDeleteName, currentDeleteUrl);
            
            if (deleteNameSpan) {
                deleteNameSpan.textContent = currentDeleteName;
            }
            
            modal.show();
        });
    });
    
    // 确认删除事件
    if (confirmDeleteBtn) {
        // 移除旧的事件监听器
        const newConfirmBtn = confirmDeleteBtn.cloneNode(true);
        confirmDeleteBtn.parentNode.replaceChild(newConfirmBtn, confirmDeleteBtn);
        
        // 绑定新的事件监听器
        document.getElementById('confirmDelete').addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            if (!currentDeleteUrl) {
                showAlert('danger', '删除URL无效');
                return;
            }
            
            // 禁用按钮，显示加载状态
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>删除中...';
            
            // 获取CSRF token
            const csrfToken = getCSRFToken();
            
            console.log('发送删除请求:', currentDeleteUrl);
            console.log('CSRF Token:', csrfToken);
            
            // 发送删除请求
            fetch(currentDeleteUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                    'Accept': 'application/json'
                },
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return response.json();
            })
            .then(data => {
                console.log('删除响应:', data);
                
                if (data.success) {
                    showAlert('success', data.message || '删除成功');
                    // 延迟刷新页面
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showAlert('danger', data.message || '删除失败');
                    this.disabled = false;
                    this.innerHTML = '确认删除';
                }
            })
            .catch(error => {
                console.error('删除请求错误:', error);
                showAlert('danger', `删除失败: ${error.message}`);
                this.disabled = false;
                this.innerHTML = '确认删除';
            });
            
            // 隐藏模态框
            modal.hide();
        });
    }
}

function getCSRFToken() {
    // 尝试多种方式获取CSRF token
    const metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) {
        return metaToken.getAttribute('content');
    }
    
    const inputToken = document.querySelector('input[name="csrf_token"]');
    if (inputToken) {
        return inputToken.value;
    }
    
    // 最后尝试模板变量
    return '{{ csrf_token() }}';
}

function showAlert(type, message) {
    // 移除现有的alert
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());
    
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" style="margin-bottom: 1rem;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertAdjacentHTML('afterbegin', alertHtml);
        
        // 滚动到顶部显示消息
        window.scrollTo({ top: 0, behavior: 'smooth' });
        
        // 自动关闭
        setTimeout(() => {
            const alert = document.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}
</script>
{% endblock %}
